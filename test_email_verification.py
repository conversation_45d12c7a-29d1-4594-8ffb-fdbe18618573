"""
测试 AugmentCode 邮箱验证码获取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from augmentcode_email_handler import AugmentCodeEmailHandler
from logger import logging

def test_get_verification_code():
    """测试获取验证码"""
    # 使用配置文件中的邮箱地址
    email_address = "<EMAIL>"

    try:
        # 创建邮箱处理器
        handler = AugmentCodeEmailHandler(email_address)

        # 手动设置用户名为 jun-temp2
        handler.username = "jun-temp2"

        # 获取验证码
        logging.info("开始获取 AugmentCode 验证码...")
        verification_code = handler.get_augmentcode_verification_code(max_retries=3, retry_interval=10)

        if verification_code:
            logging.info(f"✅ 成功获取验证码: {verification_code}")
            return verification_code
        else:
            logging.error("❌ 未能获取到验证码")
            return None

    except Exception as e:
        logging.error(f"❌ 获取验证码时发生错误: {e}")
        return None

if __name__ == "__main__":
    code = test_get_verification_code()
    if code:
        print(f"验证码: {code}")
    else:
        print("获取验证码失败")
