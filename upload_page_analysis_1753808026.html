<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="stylesheet" href="/assets/root-CUUSY10I.css"><link rel="stylesheet" href="/assets/tailwind-Dz105EZy.css"><script type="text/javascript" crossorigin="anonymous" async="" src="https://us.i.posthog.com/static/array.js"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.3/twitter-ads.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js" async="" status="loaded"></script><script type="text/javascript">(function() {
    var i = "analytics",
        analytics = window[i] = window[i] || [];
    if (!analytics.initialize) {
      if (analytics.invoked) {
        window.console && console.error && console.error("Segment snippet included twice.");
      } else {
        analytics.invoked = true;
        analytics.methods = [
          "trackSubmit", "trackClick", "trackLink", "trackForm", "pageview",
          "identify", "reset", "group", "track", "ready", "alias", "debug",
          "page", "screen", "once", "off", "on", "addSourceMiddleware",
          "addIntegrationMiddleware", "setAnonymousId", "addDestinationMiddleware",
          "register"
        ];
        analytics.factory = function(method) {
          return function() {
            if (window[i].initialized) {
              return window[i][method].apply(window[i], arguments);
            }
            var args = Array.prototype.slice.call(arguments);
            if (["track", "screen", "alias", "group", "page", "identify"].indexOf(method) > -1) {
              var canonicalLink = document.querySelector("link[rel='canonical']");
              args.push({
                __t: "bpc",
                c: (canonicalLink && canonicalLink.getAttribute("href")) || void 0,
                p: location.pathname,
                u: location.href,
                s: location.search,
                t: document.title,
                r: document.referrer
              });
            }
            args.unshift(method);
            analytics.push(args);
            return analytics;
          };
        };
        for (var n = 0; n < analytics.methods.length; n++) {
          var key = analytics.methods[n];
          analytics[key] = analytics.factory(key);
        }
        analytics.load = function(key, options) {
          var script = document.createElement("script");
          script.type = "text/javascript";
          script.async = true;
          script.setAttribute("data-global-segment-analytics-key", i);
          script.src = "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js";
          var firstScript = document.getElementsByTagName("script")[0];
          firstScript.parentNode.insertBefore(script, firstScript);
          analytics._loadOptions = options;
        };
        analytics._cdn = "https://evs.grdt.augmentcode.com";
        analytics._writeKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
        analytics.SNIPPET_VERSION = "5.2.0";
        analytics.load(analytics._writeKey);
        analytics.ready(() => {
          window.posthog.init(
            "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
            {
              api_host: "https://us.i.posthog.com",
              segment: window.analytics,
              capture_pageview: false,
              capture_pageleave: true,
            }
          );
          if (window.analyticsInitialized) {
            window.analyticsInitialized.resolve();
          } else {
            console.error("analytics deferred promise not found");
          }
        });
      }
    }
  })();
  </script><script type="text/javascript">(function(document, posthog) {
    var methodList, methodIndex, scriptElement, firstScript;
    if (!posthog.__SV) {
      window.posthog = posthog;
      posthog._i = [];
      posthog.init = function(apiKey, config, namespace) {
        // Create a stub function that collects method calls until the real library loads.
        function createStub(target, methodName) {
          var parts = methodName.split(".");
          if (parts.length === 2) {
            target = target[parts[0]];
            methodName = parts[1];
          }
          target[methodName] = function() {
            target.push([methodName].concat(Array.prototype.slice.call(arguments, 0)));
          };
        }
        // Create and insert the script element to load the PostHog library.
        scriptElement = document.createElement("script");
        scriptElement.type = "text/javascript";
        scriptElement.crossOrigin = "anonymous";
        scriptElement.async = true;
        scriptElement.src = config.api_host + "/static/array.js";
        firstScript = document.getElementsByTagName("script")[0];
        firstScript.parentNode.insertBefore(scriptElement, firstScript);
        // Initialize the PostHog namespace.
        var ph = posthog;
        if (namespace !== undefined) {
          ph = posthog[namespace] = [];
        } else {
          namespace = "posthog";
        }
        ph.people = ph.people || [];
        ph.toString = function(stub) {
          var label = "posthog";
          if (namespace !== "posthog") {
            label += "." + namespace;
          }
          if (!stub) {
            label += " (stub)";
          }
          return label;
        };
        ph.people.toString = function() {
          return ph.toString(1) + ".people (stub)";
        };
        // List of methods to be stubbed until the library loads.
        methodList = "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(" ");
        for (methodIndex = 0; methodIndex < methodList.length; methodIndex++) {
          createStub(ph, methodList[methodIndex]);
        }
        // Store initialization arguments for later use.
        posthog._i.push([apiKey, config, namespace]);
      };
      posthog.__SV = 1;
    }
  })(document, window.posthog || []);
  </script><script>window.FEATURE_FLAGS = {}</script></head><body><div data-is-root-theme="true" data-accent-color="indigo" data-gray-color="slate" data-has-background="true" data-panel-background="translucent" data-radius="medium" data-scaling="100%" class="radix-themes"><div data-is-root-theme="false" data-accent-color="crimson" data-gray-color="sand" data-has-background="false" data-panel-background="solid" data-radius="medium" data-scaling="95%" class="radix-themes"><div class="rt-Container rt-r-size-2"><div class="rt-ContainerInner"><div class="rt-reset rt-BaseCard rt-Card rt-r-size-1 rt-variant-surface rt-r-m-4"><div class="rt-Flex rt-r-fd-column rt-r-ai-center rt-r-jc-center"><img src="/augie-error.svg" alt="Augment Error" width="64" height="64"><span class="rt-Text rt-r-size-6 rt-r-weight-bold rt-r-mt-2">Error 404</span><span class="rt-Text rt-r-size-5 rt-r-mt-2 rt-r-mb-2">The page you are looking for does not exist.</span><span data-accent-color="gray" class="rt-Text rt-r-size-2 rt-r-ta-center rt-r-mb-4">Time: 2025-07-29T16:53:10.926Z</span><a href="/logout"><button data-accent-color="" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-surface rt-Button">Go Back</button></a></div></div></div></div></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events: none;"><ol tabindex="-1" class="ToastViewport"></ol></div></div><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/web-vitals.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/surveys.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/recorder.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js"></script><script>((u,v)=>{if(!window.history.state||!window.history.state.key){let w=Math.random().toString(32).slice(2);window.history.replaceState({key:w},"")}try{let x=JSON.parse(sessionStorage.getItem(u)||"{}")[v||window.history.state.key];typeof x=="number"&&window.scrollTo(0,x)}catch(w){console.error(w),sessionStorage.removeItem(u)}})("positions", null)</script><link rel="modulepreload" href="/assets/manifest-939b061e.js"><link rel="modulepreload" href="/assets/entry.client-C92V1BwZ.js"><link rel="modulepreload" href="/assets/index-Bi4s4-Io.js"><link rel="modulepreload" href="/assets/index-BS38kjqr.js"><link rel="modulepreload" href="/assets/index-B7Ui2t93.js"><link rel="modulepreload" href="/assets/components--HLsvfrm.js"><link rel="modulepreload" href="/assets/QueryClientProvider-CeGnmbe-.js"><link rel="modulepreload" href="/assets/queryClient.client-Cd_-5B4Z.js"><link rel="modulepreload" href="/assets/client-only-C74SDDMq.js"><link rel="modulepreload" href="/assets/index.modern-D0ULO_Ox.js"><link rel="modulepreload" href="/assets/theme-C1ulz75E.js"><link rel="modulepreload" href="/assets/container-BlJCmUTg.js"><link rel="modulepreload" href="/assets/card-BBgKeY7L.js"><link rel="modulepreload" href="/assets/link-CMt6MnuB.js"><link rel="modulepreload" href="/assets/flex-C9XhsxSj.js"><link rel="modulepreload" href="/assets/button-Dvrjyl3p.js"><link rel="modulepreload" href="/assets/Toast-BYZM3h9e.js"><link rel="modulepreload" href="/assets/index-DrFu-skq.js"><link rel="modulepreload" href="/assets/jotaiStore.client-C8G6aWco.js"><link rel="modulepreload" href="/assets/react-CDazRDxk.js"><link rel="modulepreload" href="/assets/constants-pIwcS3Kh.js"><link rel="modulepreload" href="/assets/index-CI4icoal.js"><link rel="modulepreload" href="/assets/index-CAuWbg93.js"><link rel="modulepreload" href="/assets/spinner-Cq6egsy4.js"><link rel="modulepreload" href="/assets/index-CAyM6kBC.js"><link rel="modulepreload" href="/assets/get-subtree-8AxxbxX_.js"><link rel="modulepreload" href="/assets/base-button-Dk95TXPu.js"><link rel="modulepreload" href="/assets/index-B5mzPb5P.js"><link rel="modulepreload" href="/assets/react-icons.esm-g3l3pVh3.js"><link rel="modulepreload" href="/assets/root-IpHfnGBc.js"><script> </script><script type="module" async=""> </script></body></html>