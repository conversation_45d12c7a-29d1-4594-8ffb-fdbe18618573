{"name": "augmentcode-testing-framework", "version": "1.0.0", "description": "AugmentCode 地区限制绕过测试框架 - 基于 Puppeteer + Stealth 的完整指纹伪造实现", "main": "augmentcode_testing_framework.js", "scripts": {"test": "node augmentcode_testing_framework.js", "install-deps": "npm install", "setup": "npm install && node -e \"console.log('测试框架依赖安装完成')\"", "debug": "node --inspect augmentcode_testing_framework.js", "headless": "node -e \"const Framework = require('./augmentcode_testing_framework.js'); (async () => { const f = new Framework({headless: true}); await f.initialize(); await f.runRegistrationTest('<EMAIL>'); await f.cleanup(); })();\""}, "keywords": ["puppeteer", "stealth", "fingerprint", "automation", "testing", "augmentcode", "bypass", "anti-detection"], "author": "RIPER-5 Testing Framework", "license": "MIT", "dependencies": {"puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer": "^21.6.1"}, "devDependencies": {"eslint": "^8.55.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "local"}