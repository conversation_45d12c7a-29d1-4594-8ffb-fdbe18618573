from nodriver import *
import nodriver.cdp.emulation as emulation
import json

async def main():
    # 使用最小化配置启动浏览器，避免被人机验证检测
    browser = await start(
        headless=False,
        lang="en-US"  # 设置浏览器语言为美国英语
    )

    # 创建带代理的浏览器上下文
    proxied_tab = await browser.create_context(
        proxy_server="http://127.0.0.1:7897"  # 您的代理服务器
    )

    # 检测代理IP的地理位置
    print("正在检测代理IP的地理位置...")
    try:
        # 通过代理访问IP地理位置API
        await proxied_tab.get('https://ipapi.co/json/')

        # 等待页面加载完成
        await proxied_tab.sleep(2)

        # 获取页面内容（JSON格式的地理位置信息）
        ip_info_text = await proxied_tab.evaluate('document.body.innerText')
        ip_info = json.loads(ip_info_text)

        # 提取地理位置信息
        latitude = float(ip_info.get('latitude', 39.8283))  # 默认美国中部
        longitude = float(ip_info.get('longitude', -98.5795))
        timezone = ip_info.get('timezone', 'America/Chicago')
        city = ip_info.get('city', 'Unknown')
        region = ip_info.get('region', 'Unknown')
        country = ip_info.get('country_name', 'Unknown')

        print(f"检测到IP位置: {city}, {region}, {country}")
        print(f"坐标: {latitude}, {longitude}")
        print(f"时区: {timezone}")

    except Exception as e:
        print(f"IP地理位置检测失败，使用默认美国中部位置: {e}")
        # 默认使用美国中部位置（堪萨斯州）
        latitude = 39.8283
        longitude = -98.5795
        timezone = "America/Chicago"

    # 根据检测到的位置设置地理位置
    await proxied_tab.send(emulation.set_geolocation_override(
        latitude=latitude,
        longitude=longitude,
        accuracy=100
    ))

    # 根据检测到的时区设置时区
    await proxied_tab.send(emulation.set_timezone_override(timezone))

    # 设置真实的浏览器环境，专门针对 reCAPTCHA 和 Verisoul 检测
    print("正在设置真实浏览器环境...")
    await proxied_tab.evaluate('''
        // 移除自动化检测标识
        delete window.navigator.webdriver;
        delete window.webdriver;
        if (window.chrome && window.chrome.runtime) {
            delete window.chrome.runtime.onConnect;
        }

        // 设置真实的硬件并发数（CPU核心数）
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => 8,
            configurable: true
        });

        // 设置内存信息
        if (!navigator.deviceMemory) {
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
                configurable: true
            });
        }

        // 设置屏幕信息为常见的1920x1080分辨率
        Object.defineProperty(screen, 'width', {
            get: () => 1920,
            configurable: true
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080,
            configurable: true
        });
        Object.defineProperty(screen, 'availWidth', {
            get: () => 1920,
            configurable: true
        });
        Object.defineProperty(screen, 'availHeight', {
            get: () => 1040,
            configurable: true
        });
        Object.defineProperty(screen, 'colorDepth', {
            get: () => 24,
            configurable: true
        });
        Object.defineProperty(screen, 'pixelDepth', {
            get: () => 24,
            configurable: true
        });

        // 模拟真实的插件信息
        Object.defineProperty(navigator, 'plugins', {
            get: () => {
                const plugins = [];
                plugins[0] = {
                    name: 'Chrome PDF Plugin',
                    filename: 'internal-pdf-viewer',
                    description: 'Portable Document Format',
                    length: 1
                };
                plugins[1] = {
                    name: 'Chrome PDF Viewer',
                    filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                    description: '',
                    length: 1
                };
                plugins.length = 2;
                return plugins;
            },
            configurable: true
        });

        // 设置 WebGL 信息为常见的Intel显卡
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                return 'Intel Inc.';
            }
            if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                return 'Intel(R) UHD Graphics 620';
            }
            return originalGetParameter.call(this, parameter);
        };

        // 添加真实的鼠标和键盘事件监听器来模拟人类行为
        let mouseMovements = 0;
        let keystrokes = 0;
        let clickCount = 0;

        document.addEventListener('mousemove', () => {
            mouseMovements++;
        }, true);

        document.addEventListener('keydown', () => {
            keystrokes++;
        }, true);

        document.addEventListener('click', () => {
            clickCount++;
        }, true);

        // 模拟人类行为模式数据
        window.humanBehavior = {
            mouseMovements: () => mouseMovements,
            keystrokes: () => keystrokes,
            clicks: () => clickCount,
            startTime: Date.now(),
            getActiveTime: () => Date.now() - window.humanBehavior.startTime
        };

        // 添加随机的鼠标移动来模拟真实用户
        setTimeout(() => {
            const event = new MouseEvent('mousemove', {
                clientX: Math.random() * window.innerWidth,
                clientY: Math.random() * window.innerHeight,
                bubbles: true
            });
            document.dispatchEvent(event);
        }, Math.random() * 2000 + 1000);
    ''')

    print("正在打开 https://www.augmentcode.com/blog ...")
    await proxied_tab.get('https://www.augmentcode.com/blog')

    print("页面已打开，等待您的操作验证...")
    print("浏览器将保持打开状态，您可以在浏览器中进行验证操作")

    # 保持浏览器打开，等待用户操作
    try:
        while True:
            await browser.sleep(1)  # 每秒检查一次
    except KeyboardInterrupt:
        print("检测到 Ctrl+C，正在关闭浏览器...")
        await browser.close()

if __name__ == '__main__':
    loop().run_until_complete(main())
