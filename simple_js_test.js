/**
 * AugmentCode 简化版JavaScript测试脚本
 * 使用原生Puppeteer进行基础反检测测试
 * 
 * 创建于: 2025-07-29
 * 协议: RIPER-5 v5.0 简化版测试
 */

const puppeteer = require('./node_modules/puppeteer-core/lib/cjs/puppeteer/puppeteer-core.js');
const fs = require('fs');

class SimpleAugmentCodeTest {
    constructor(email = '<EMAIL>') {
        this.email = email;
        this.browser = null;
        this.page = null;
    }

    /**
     * 初始化浏览器
     */
    async initBrowser() {
        console.log('🚀 启动简化版JavaScript测试...');
        
        try {
            this.browser = await puppeteer.launch({
                headless: false, // 显示浏览器窗口
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ],
                defaultViewport: { width: 1920, height: 1080 }
            });

            this.page = await this.browser.newPage();
            
            // 基础反检测设置
            await this.setupAntiDetection();
            
            console.log('✅ 浏览器初始化成功');
            return true;
        } catch (error) {
            console.error('❌ 浏览器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置基础反检测
     */
    async setupAntiDetection() {
        // 移除webdriver标识
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });

        // 修改Chrome对象
        await this.page.evaluateOnNewDocument(() => {
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
        });

        // 修改权限查询
        await this.page.evaluateOnNewDocument(() => {
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        });

        // Canvas指纹噪声注入
        await this.page.evaluateOnNewDocument(() => {
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {
                const context = this.getContext('2d');
                if (context) {
                    try {
                        const imageData = context.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            imageData.data[i] += Math.floor(Math.random() * 3) - 1;     // R
                            imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1; // G  
                            imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1; // B
                        }
                        context.putImageData(imageData, 0, 0);
                    } catch(e) {}
                }
                return originalToDataURL.apply(this, arguments);
            };
        });

        console.log('✅ 基础反检测设置完成');
    }

    /**
     * 模拟真实用户行为
     */
    async simulateHumanBehavior() {
        try {
            // 随机鼠标移动
            for (let i = 0; i < 3; i++) {
                const x = Math.random() * 1800 + 100;
                const y = Math.random() * 900 + 100;
                await this.page.mouse.move(x, y, { steps: 10 });
                await this.page.waitForTimeout(Math.random() * 1000 + 500);
            }

            // 随机滚动
            await this.page.evaluate(() => {
                window.scrollBy(0, Math.random() * 500 + 200);
            });

            await this.page.waitForTimeout(2000);
            console.log('✅ 用户行为模拟完成');
        } catch (error) {
            console.error('❌ 用户行为模拟失败:', error);
        }
    }

    /**
     * 测试AugmentCode注册流程
     */
    async testRegistration() {
        try {
            console.log('🌐 访问 AugmentCode 网站...');
            await this.page.goto('https://augmentcode.com/', { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });

            // 模拟用户行为
            await this.simulateHumanBehavior();

            console.log('📋 页面加载完成，等待手动测试...');
            console.log('💡 您现在可以在浏览器中手动进行注册测试');
            console.log('🔍 观察是否能通过Cloudflare验证');
            console.log('⏹️  按 Ctrl+C 结束测试');

            // 保持浏览器运行
            await this.keepAlive();

        } catch (error) {
            console.error('❌ 注册测试失败:', error);
        }
    }

    /**
     * 保持浏览器运行
     */
    async keepAlive() {
        return new Promise((resolve) => {
            process.on('SIGINT', async () => {
                console.log('\n🛑 收到中断信号，正在关闭浏览器...');
                await this.cleanup();
                resolve();
            });
        });
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            if (this.browser) {
                await this.browser.close();
                console.log('✅ 浏览器已关闭');
            }
        } catch (error) {
            console.error('❌ 清理资源失败:', error);
        }
    }

    /**
     * 运行完整测试
     */
    async run() {
        console.log('=' * 60);
        console.log('🧪 AugmentCode 简化版JavaScript测试');
        console.log(`📧 测试邮箱: ${this.email}`);
        console.log('=' * 60);

        const success = await this.initBrowser();
        if (!success) {
            console.error('❌ 测试初始化失败');
            return;
        }

        await this.testRegistration();
    }
}

// 运行测试
async function main() {
    const email = process.argv[2] || '<EMAIL>';
    const test = new SimpleAugmentCodeTest(email);
    await test.run();
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimpleAugmentCodeTest;
