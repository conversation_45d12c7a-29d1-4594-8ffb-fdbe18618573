<!DOCTYPE html><html lang="en"><head><meta http-equiv="origin-trial" content="A7vZI3v+Gz7JfuRolKNM4Aff6zaGuT7X0mf3wtoZTnKv6497cVMnhy03KDqX7kBz/q/iidW7srW31oQbBt4VhgoAAACUeyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGUuY29tOjQ0MyIsImZlYXR1cmUiOiJEaXNhYmxlVGhpcmRQYXJ0eVN0b3JhZ2VQYXJ0aXRpb25pbmczIiwiZXhwaXJ5IjoxNzU3OTgwODAwLCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="stylesheet" href="/assets/root-CUUSY10I.css"><link rel="stylesheet" href="/assets/tailwind-DxnphuB3.css"><script type="text/javascript" crossorigin="anonymous" async="" src="https://us.i.posthog.com/static/array.js"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.3/twitter-ads.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" async="" charset="utf-8" src="https://www.gstatic.com/recaptcha/releases/ngcIAHyEnHQZZIKkyKneDTW3/recaptcha__ja.js" crossorigin="anonymous" integrity="sha384-DtPxW+pdgKqvI21j3BwUzFsDSwRueP4WE8NuZJ0Y9e7nYQcHNd6/aT3HGPFit5JA"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js" async="" status="loaded"></script><script type="text/javascript" async="" data-global-segment-analytics-key="analytics" src="https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js"></script><script type="text/javascript">(function() {
    var i = "analytics",
        analytics = window[i] = window[i] || [];
    if (!analytics.initialize) {
      if (analytics.invoked) {
        window.console && console.error && console.error("Segment snippet included twice.");
      } else {
        analytics.invoked = true;
        analytics.methods = [
          "trackSubmit", "trackClick", "trackLink", "trackForm", "pageview",
          "identify", "reset", "group", "track", "ready", "alias", "debug",
          "page", "screen", "once", "off", "on", "addSourceMiddleware",
          "addIntegrationMiddleware", "setAnonymousId", "addDestinationMiddleware",
          "register"
        ];
        analytics.factory = function(method) {
          return function() {
            if (window[i].initialized) {
              return window[i][method].apply(window[i], arguments);
            }
            var args = Array.prototype.slice.call(arguments);
            if (["track", "screen", "alias", "group", "page", "identify"].indexOf(method) > -1) {
              var canonicalLink = document.querySelector("link[rel='canonical']");
              args.push({
                __t: "bpc",
                c: (canonicalLink && canonicalLink.getAttribute("href")) || void 0,
                p: location.pathname,
                u: location.href,
                s: location.search,
                t: document.title,
                r: document.referrer
              });
            }
            args.unshift(method);
            analytics.push(args);
            return analytics;
          };
        };
        for (var n = 0; n < analytics.methods.length; n++) {
          var key = analytics.methods[n];
          analytics[key] = analytics.factory(key);
        }
        analytics.load = function(key, options) {
          var script = document.createElement("script");
          script.type = "text/javascript";
          script.async = true;
          script.setAttribute("data-global-segment-analytics-key", i);
          script.src = "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js";
          var firstScript = document.getElementsByTagName("script")[0];
          firstScript.parentNode.insertBefore(script, firstScript);
          analytics._loadOptions = options;
        };
        analytics._cdn = "https://evs.grdt.augmentcode.com";
        analytics._writeKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
        analytics.SNIPPET_VERSION = "5.2.0";
        analytics.load(analytics._writeKey);
        analytics.ready(() => {
          window.posthog.init(
            "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
            {
              api_host: "https://us.i.posthog.com",
              segment: window.analytics,
              capture_pageview: false,
              capture_pageleave: true,
            }
          );
          if (window.analyticsInitialized) {
            window.analyticsInitialized.resolve();
          } else {
            console.error("analytics deferred promise not found");
          }
        });
      }
    }
  })();
  </script><script type="text/javascript">(function(document, posthog) {
    var methodList, methodIndex, scriptElement, firstScript;
    if (!posthog.__SV) {
      window.posthog = posthog;
      posthog._i = [];
      posthog.init = function(apiKey, config, namespace) {
        // Create a stub function that collects method calls until the real library loads.
        function createStub(target, methodName) {
          var parts = methodName.split(".");
          if (parts.length === 2) {
            target = target[parts[0]];
            methodName = parts[1];
          }
          target[methodName] = function() {
            target.push([methodName].concat(Array.prototype.slice.call(arguments, 0)));
          };
        }
        // Create and insert the script element to load the PostHog library.
        scriptElement = document.createElement("script");
        scriptElement.type = "text/javascript";
        scriptElement.crossOrigin = "anonymous";
        scriptElement.async = true;
        scriptElement.src = config.api_host + "/static/array.js";
        firstScript = document.getElementsByTagName("script")[0];
        firstScript.parentNode.insertBefore(scriptElement, firstScript);
        // Initialize the PostHog namespace.
        var ph = posthog;
        if (namespace !== undefined) {
          ph = posthog[namespace] = [];
        } else {
          namespace = "posthog";
        }
        ph.people = ph.people || [];
        ph.toString = function(stub) {
          var label = "posthog";
          if (namespace !== "posthog") {
            label += "." + namespace;
          }
          if (!stub) {
            label += " (stub)";
          }
          return label;
        };
        ph.people.toString = function() {
          return ph.toString(1) + ".people (stub)";
        };
        // List of methods to be stubbed until the library loads.
        methodList = "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(" ");
        for (methodIndex = 0; methodIndex < methodList.length; methodIndex++) {
          createStub(ph, methodList[methodIndex]);
        }
        // Store initialization arguments for later use.
        posthog._i.push([apiKey, config, namespace]);
      };
      posthog.__SV = 1;
    }
  })(document, window.posthog || []);
  </script><script>window.FEATURE_FLAGS = {
  "auth_central_user_tier_change": true,
  "team_management": true,
  "team_management_canary_domains": "augm.io,turing.com",
  "customer_ui_enable_user_feature_stats": true,
  "customer_ui_windsurf_promotion_enabled": true,
  "customer_ui_cursor_promotion_enabled": true,
  "customer_ui_content_deletion_enabled": true
}</script><script id="recaptcha-enterprise-script" src="https://www.google.com/recaptcha/enterprise.js?render=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1" async=""></script></head><body><div data-is-root-theme="true" data-accent-color="indigo" data-gray-color="slate" data-has-background="true" data-panel-background="translucent" data-radius="medium" data-scaling="100%" class="radix-themes"><div class="rt-Container rt-r-size-4 rt-r-mx-4"><div class="rt-ContainerInner"><div class="rt-Box" style="margin-bottom: 24px;"><div class="rt-Box base-header-container "><style scoped="">@scope {
  /* Scoped styles for BaseHeader component */
  :scope.base-header-container {
    border-radius: 0;
    overflow: hidden;
    padding: 18px 0;
    width: 100%;
    /* Prevent layout shift during hydration */
    contain: layout style;
    
      position: sticky;
      top: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.95);
    
  }
  
  :scope .base-header-content {
    margin: 0 auto;
    width: 100%;
    padding: 0 32px;
    /* Ensure consistent layout during loading */
    min-height: 38px;
  }
  
  :scope .base-header-logo-section {
    position: relative;
  }
  
  :scope .base-header-logo {
    width: 140px;
    height: 38px;
  }
  
  :scope .base-header-logo-link {
    display: inline-block;
    transition: opacity 0.2s ease;
    cursor: pointer;
  }
  
  :scope .base-header-logo-link:hover {
    opacity: 0.8;
  }
  
  /* Email styles */
  :scope .base-header-email {
    opacity: 0.9;
    font-weight: 500;
    font-size: 15px;
  }
  
  /* Logout button styles */
  :scope .base-header-logout-button {
    transition: all 0.2s ease;
    font-weight: 600;
    font-size: 15px;
    opacity: 0.9;
    padding: 8px 16px;
  }
  
  /* Right section container to prevent layout shift */
  :scope .base-header-right-section {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px; /* Prevent collapse during loading */
    justify-content: flex-end;
  }
  
  /* Logo section styles */
  :scope .base-header-logo-section {
    position: relative;
  }
}</style><div class="rt-Box base-header-content" style="max-width: 1200px;"><div class="rt-Flex rt-r-fd-column sm:rt-r-fd-row rt-r-ai-start sm:rt-r-ai-center rt-r-jc-space-between rt-r-gap-3 sm:rt-r-gap-0"><div class="rt-Flex rt-r-ai-center rt-r-gap-4 rt-r-py-1 base-header-logo-section"><img src="/augment-logo.svg" alt="Augment Logo" class="base-header-logo"></div><div class="rt-Flex rt-r-ai-center rt-r-gap-3 base-header-right-section"><span data-accent-color="gray" class="rt-Text rt-r-size-2 base-header-email"><EMAIL></span><div class="rt-Box"><form method="get" action="/logout" data-discover="true"><button data-accent-color="gray" type="submit" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-soft rt-Button base-header-logout-button">Logout</button></form></div></div></div></div></div></div><div class="rt-Box" style="max-width: 1200px; margin: 0px auto; width: 100%; padding: 0px 32px;"><div class="rt-Box" style="width: 100%;"><style scoped="">@scope {
  :scope {
    padding-bottom: var(--ds-spacing-10);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-5);
  
    .subtle-text {
      color: var(--ds-color-neutral-11);
      font-size: var(--ds-font-size-2);
      line-height: 1.5;
    }
  
    .note-text {
      font-style: italic;
      padding: var(--ds-spacing-4);
      background: var(--ds-color-panel);
      border-radius: var(--ds-radius-2);
      border-left: 3px solid var(--blue-8);
    }
  }
}</style><div class="rt-Box"><div class="promotion-header"><style scoped="">@scope {
  :scope {
              .promotion-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: var(--ds-spacing-6) 0 var(--ds-spacing-8) 0;
              }
  
              .promotion-title {
                margin-bottom: var(--ds-spacing-4);
              }
  
              .promotion-header .promotion-description {
                display: flex;
                gap: var(--ds-spacing-2);
                font-size: var(--ds-font-size-3);
  font-weight: var(--ds-font-weight-regular);
  line-height: var(--ds-line-height-3);
  letter-spacing: var(--ds-letter-spacing-3);
  color: var(--ds-color-text-default);
                color: var(--ds-color-text-subtle);
                line-height: 1.6;
              }
  
              .subtle-text {
                color: var(--ds-color-neutral-11);
                font-size: var(--ds-font-size-2);
              }
            }
}</style><div><h1 class="rt-Heading rt-r-size-8 rt-r-weight-bold promotion-title">Welcome to Augment</h1><div class="promotion-description"><span class="rt-Text rt-r-size-3 subtle-text">Making the switch from Cursor? We're excited to have you! Verify your Cursor customer status to get started with 600 free user messages.</span></div></div></div></div><div class="rt-reset rt-BaseCard rt-Card rt-r-size-3 rt-variant-surface"><div class="rt-Flex rt-r-fd-column rt-r-gap-4"><style scoped="">@scope {
  :scope {
    .drag-drop-zone {
      border: 2px dashed var(--ds-color-border-default);
      border-radius: var(--ds-radius-4);
      padding: var(--ds-spacing-8);
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: var(--ds-color-background-default);
      min-height: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--ds-spacing-3);
    }
  
    .drag-drop-zone:hover:not(.disabled) {
      border-color: var(--ds-color-accent-7);
      background-color: var(--ds-color-accent-2);
    }
  
    .drag-drop-zone.is-dragging {
      border-color: var(--ds-color-accent-9);
      background-color: var(--ds-color-accent-3);
      border-style: solid;
    }
  
    .drag-drop-zone.has-file {
      border-color: var(--ds-color-success-7);
      background-color: var(--ds-color-success-2);
    }
  
    .drag-drop-zone.has-error {
      border-color: var(--ds-color-error-7);
      background-color: var(--ds-color-error-2);
    }
  
    .drag-drop-zone.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  
    .file-input {
      display: none;
    }
  
    .validation-error {
      background-color: var(--ds-color-error-2);
      border: 1px solid var(--ds-color-error-6);
      border-radius: var(--ds-radius-2);
      padding: var(--ds-spacing-2);
      margin-top: var(--ds-spacing-2);
      color: var(--ds-color-error-11);
    }
  
    .upload-icon {
      width: 48px;
      height: 48px;
      color: var(--ds-color-neutral-9);
      margin-bottom: var(--ds-spacing-2);
    }
  
    .subtle-text {
      color: var(--ds-color-neutral-11);
      font-size: var(--ds-font-size-2);
    }
  }
}</style><div class="rt-Flex rt-r-fd-column rt-r-gap-4"><div class="rt-Box"><div class="drag-drop-zone    " role="button" tabindex="0" aria-label="Upload PDF file" aria-disabled="false"><svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg><div><span class="rt-Text rt-r-size-4 rt-r-weight-medium" style="display: block; margin-bottom: 8px;">Drag &amp; drop your PDF here</span><span class="rt-Text rt-r-size-3 subtle-text" style="display: block; margin-bottom: 8px;">or click to browse files</span><span class="rt-Text rt-r-size-2 subtle-text">PDF files only, max 1MB</span></div></div><input type="file" name="invoice" accept=".pdf" class="file-input"></div><div class="rt-Flex rt-r-jc-end"><button data-disabled="true" data-accent-color="" type="button" class="rt-reset rt-BaseButton rt-r-size-2 rt-variant-solid rt-Button" disabled="">Upload Invoice</button></div></div></div></div><div class="note-text"><span class="rt-Text rt-r-size-3 subtle-text" style="line-height: 1.5;"><strong>Note:</strong> This offer is only available to new customers who have not enrolled in any other promotion.</span></div></div></div></div></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="ToastViewport"></ol></div></div><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/surveys.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/web-vitals.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.258.2"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/recorder.js?v=1.258.2"></script><script>((STORAGE_KEY, restoreKey) => {
    if (!window.history.state || !window.history.state.key) {
      let key = Math.random().toString(32).slice(2);
      window.history.replaceState({
        key
      }, "");
    }
    try {
      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || "{}");
      let storedY = positions[restoreKey || window.history.state.key];
      if (typeof storedY === "number") {
        window.scrollTo(0, storedY);
      }
    } catch (error) {
      console.error(error);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  })("positions", null)</script><link rel="modulepreload" href="/assets/manifest-939b061e.js"><link rel="modulepreload" href="/assets/entry.client-C92V1BwZ.js"><link rel="modulepreload" href="/assets/index-Bi4s4-Io.js"><link rel="modulepreload" href="/assets/index-BS38kjqr.js"><link rel="modulepreload" href="/assets/index-B7Ui2t93.js"><link rel="modulepreload" href="/assets/components--HLsvfrm.js"><link rel="modulepreload" href="/assets/QueryClientProvider-CeGnmbe-.js"><link rel="modulepreload" href="/assets/queryClient.client-Cd_-5B4Z.js"><link rel="modulepreload" href="/assets/client-only-C74SDDMq.js"><link rel="modulepreload" href="/assets/index.modern-D0ULO_Ox.js"><link rel="modulepreload" href="/assets/theme-C1ulz75E.js"><link rel="modulepreload" href="/assets/container-BlJCmUTg.js"><link rel="modulepreload" href="/assets/card-BBgKeY7L.js"><link rel="modulepreload" href="/assets/link-CMt6MnuB.js"><link rel="modulepreload" href="/assets/flex-C9XhsxSj.js"><link rel="modulepreload" href="/assets/button-Dvrjyl3p.js"><link rel="modulepreload" href="/assets/Toast-BYZM3h9e.js"><link rel="modulepreload" href="/assets/index-DrFu-skq.js"><link rel="modulepreload" href="/assets/jotaiStore.client-C8G6aWco.js"><link rel="modulepreload" href="/assets/react-CDazRDxk.js"><link rel="modulepreload" href="/assets/constants-pIwcS3Kh.js"><link rel="modulepreload" href="/assets/index-CI4icoal.js"><link rel="modulepreload" href="/assets/index-CAuWbg93.js"><link rel="modulepreload" href="/assets/spinner-Cq6egsy4.js"><link rel="modulepreload" href="/assets/index-CAyM6kBC.js"><link rel="modulepreload" href="/assets/get-subtree-8AxxbxX_.js"><link rel="modulepreload" href="/assets/base-button-Dk95TXPu.js"><link rel="modulepreload" href="/assets/index-B5mzPb5P.js"><link rel="modulepreload" href="/assets/react-icons.esm-g3l3pVh3.js"><link rel="modulepreload" href="/assets/root-IpHfnGBc.js"><link rel="modulepreload" href="/assets/isomorphic-BzZpFA68.js"><link rel="modulepreload" href="/assets/feature-flags.client-BVZhVN7G.js"><link rel="modulepreload" href="/assets/BaseHeader-BVjP1EbY.js"><link rel="modulepreload" href="/assets/box-DvlTT8Qh.js"><link rel="modulepreload" href="/assets/user-BAXWP6JR.js"><link rel="modulepreload" href="/assets/proto3-Bmo7MjaP.js"><link rel="modulepreload" href="/assets/queryOptions-9RRPSWE3.js"><link rel="modulepreload" href="/assets/useQuery-DT1j272T.js"><link rel="modulepreload" href="/assets/number-BnNNTh_l.js"><link rel="modulepreload" href="/assets/constants-C5gnWpVx.js"><link rel="modulepreload" href="/assets/style-4fOx0d-T.js"><link rel="modulepreload" href="/assets/heading-Duq80h8F.js"><link rel="modulepreload" href="/assets/skeleton-qwMe81ym.js"><link rel="modulepreload" href="/assets/promotions.cursor-BnDnEfTx.js"><script>window.__remixContext = {"basename":"/","future":{"v3_fetcherPersist":true,"v3_relativeSplatPath":true,"v3_throwAbortReason":true,"v3_routeConfig":false,"v3_singleFetch":false,"v3_lazyRouteDiscovery":false,"unstable_optimizeDeps":false},"isSpaMode":false,"state":{"loaderData":{"root":{"earliestData":{"year":2025,"month":5,"day":27},"featureFlags":{"auth_central_user_tier_change":true,"team_management":true,"team_management_canary_domains":"augm.io,turing.com","customer_ui_enable_user_feature_stats":true,"customer_ui_windsurf_promotion_enabled":true,"customer_ui_cursor_promotion_enabled":true,"customer_ui_content_deletion_enabled":true},"user":{"userId":"4a2ff202-9bbf-46a4-9655-a5184264c240","tenantId":"91626fee8217760fcb317a4085b2b2ee","tenantName":"d1-discovery2","shardNamespace":"d1","email":"<EMAIL>","roles":[],"createdAt":1753804097167,"sessionId":"93874556-4a07-4eec-bc65-e874c9430ea2"}},"routes/promotions.cursor":{"recaptchaSiteKey":"6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1"}},"actionData":null,"errors":null}};</script><script type="module" async="">import "/assets/manifest-939b061e.js";
import * as route0 from "/assets/root-IpHfnGBc.js";
import * as route1 from "/assets/promotions.cursor-BnDnEfTx.js";

window.__remixRouteModules = {"root":route0,"routes/promotions.cursor":route1};

import("/assets/entry.client-C92V1BwZ.js");</script><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe><div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-ubpmilkusj0b" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/enterprise/anchor?ar=1&amp;k=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1&amp;co=aHR0cHM6Ly9hcHAuYXVnbWVudGNvZGUuY29tOjQ0Mw..&amp;hl=ja&amp;v=ngcIAHyEnHQZZIKkyKneDTW3&amp;size=invisible&amp;anchor-ms=20000&amp;execute-ms=15000&amp;cb=w81yq1xqsraj"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response-100000" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div><iframe style="display: none;"></iframe></div></body></html>