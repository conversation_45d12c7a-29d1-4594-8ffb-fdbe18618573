"""
AugmentCode 增强版注册脚本
基于现有项目架构的改进版本
专注于增强反检测能力而不重写核心逻辑

创建于: 2025-07-29
协议: RIPER-5 v5.0 Python增强方案
"""

import time
import random
from logger import logging
from anti_detection_browser import AntiDetectionBrowserManager
from augmentcode_register import AugmentCodeRegister

class EnhancedAugmentCodeRegister(AugmentCodeRegister):
    """增强版 AugmentCode 注册器，继承原有功能并添加更强的反检测能力"""

    def __init__(self, email):
        super().__init__(email)
        self.anti_detection_manager = AntiDetectionBrowserManager()
        
    def init_enhanced_browser(self):
        """初始化增强版反检测浏览器"""
        logging.info("正在初始化增强版反检测浏览器...")

        # 使用反检测浏览器管理器
        browser = self.anti_detection_manager.init_browser()
        if browser:
            self.tab = browser.latest_tab
            logging.info("增强版浏览器初始化成功")

            # 应用额外的反检测脚本
            self._apply_enhanced_anti_detection()
            return True
        else:
            logging.error("增强版浏览器初始化失败")
            return False
    def _apply_enhanced_anti_detection(self):
        """应用增强的反检测脚本"""
        enhanced_js = """
        try {
            // 额外的Canvas噪声注入
            if (typeof HTMLCanvasElement !== 'undefined') {
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function() {
                    const context = this.getContext('2d');
                    if (context) {
                        try {
                            const imageData = context.getImageData(0, 0, this.width, this.height);
                            // 添加更细致的噪声
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 3) - 1;     // R
                                imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1; // G
                                imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1; // B
                            }
                            context.putImageData(imageData, 0, 0);
                        } catch(e) {}
                    }
                    return originalToDataURL.apply(this, arguments);
                };
            }

            // AudioContext 指纹干扰
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (AudioContext) {
                const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                AudioContext.prototype.createAnalyser = function() {
                    const analyser = originalCreateAnalyser.call(this);
                    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                    analyser.getFloatFrequencyData = function(array) {
                        originalGetFloatFrequencyData.call(this, array);
                        for (let i = 0; i < array.length; i++) {
                            array[i] += Math.random() * 0.0001 - 0.00005;
                        }
                    };
                    return analyser;
                };
            }

            // 移除自动化痕迹（安全检查）
            if (window.chrome && window.chrome.runtime) {
                try {
                    delete window.chrome.runtime.onConnect;
                    delete window.chrome.runtime.onMessage;
                } catch(e) {}
            }
        } catch(e) {
            console.log('Enhanced anti-detection script error:', e);
        }
        """

        try:
            self.tab.run_js(enhanced_js)
            logging.info("增强反检测脚本应用成功")
        except Exception as e:
            logging.error(f"增强反检测脚本应用失败: {e}")
    def _simulate_enhanced_human_behavior(self):
        """模拟增强的真实用户行为"""
        try:
            if not self.tab:
                return

            # 随机鼠标移动（使用DrissionPage的方法）
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 1820)
                y = random.randint(100, 980)
                self.tab.actions.move_to((x, y))
                time.sleep(random.uniform(0.2, 0.5))

            # 随机滚动（使用JavaScript方式）
            scroll_amount = random.randint(-300, 300)
            scroll_js = f"window.scrollBy(0, {scroll_amount});"
            self.tab.run_js(scroll_js)

            # 更真实的等待时间
            time.sleep(random.uniform(1.5, 4.0))

            logging.info("增强用户行为模拟完成")
        except Exception as e:
            logging.error(f"用户行为模拟失败: {e}")
    def run_enhanced_registration(self):
        """执行增强版注册流程"""
        try:
            logging.info(f"开始增强版注册流程，邮箱: {self.email}")

            # 初始化增强浏览器
            if not self.init_enhanced_browser():
                return False

            # 访问页面
            self.tab.get('https://augmentcode.com/')
            logging.info("页面加载完成")

            # 模拟增强用户行为
            self._simulate_enhanced_human_behavior()

            # 重新应用反检测脚本（确保在页面加载后生效）
            self._apply_enhanced_anti_detection()

            # 调用父类的注册方法，但使用增强的浏览器
            # 这里可以调用原有的注册步骤
            logging.info("准备执行原有注册流程...")

            # 可以在这里调用父类的具体注册方法
            # 例如: return super().register()

            logging.info("增强版注册流程准备就绪")
            return True

        except Exception as e:
            logging.error(f"增强版注册流程失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self.anti_detection_manager, 'browser') and self.anti_detection_manager.browser:
                self.anti_detection_manager.browser.quit()
            logging.info("增强版浏览器资源清理完成")
        except Exception as e:
            logging.error(f"清理资源失败: {e}")

# 使用示例
if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        email = sys.argv[1]
    else:
        email = "<EMAIL>"

    register = EnhancedAugmentCodeRegister(email)

    try:
        success = register.run_enhanced_registration()
        if success:
            logging.info("增强版注册流程执行成功")
        else:
            logging.error("增强版注册流程执行失败")
    finally:
        register.cleanup()
