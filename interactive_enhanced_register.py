"""
AugmentCode 交互式增强版注册脚本
保持浏览器运行状态，允许手动测试

创建于: 2025-07-29
协议: RIPER-5 v5.0 交互式测试版本
"""

import time
import random
from logger import logging
from anti_detection_browser import AntiDetectionBrowserManager
from augmentcode_register import AugmentCodeRegister

class InteractiveEnhancedRegister(AugmentCodeRegister):
    """交互式增强版注册器，保持浏览器运行状态"""
    
    def __init__(self, email):
        super().__init__(email)
        self.anti_detection_manager = AntiDetectionBrowserManager()
        self.browser_running = False
    
    def init_enhanced_browser(self):
        """初始化增强版反检测浏览器"""
        logging.info("正在初始化增强版反检测浏览器...")
        
        # 使用反检测浏览器管理器
        browser = self.anti_detection_manager.init_browser()
        if browser:
            self.tab = browser.latest_tab
            self.browser_running = True
            logging.info("增强版浏览器初始化成功")
            
            # 应用额外的反检测脚本
            self._apply_enhanced_anti_detection()
            return True
        else:
            logging.error("增强版浏览器初始化失败")
            return False
    
    def _apply_enhanced_anti_detection(self):
        """应用增强的反检测脚本"""
        enhanced_js = """
        try {
            // 额外的Canvas噪声注入
            if (typeof HTMLCanvasElement !== 'undefined') {
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function() {
                    const context = this.getContext('2d');
                    if (context) {
                        try {
                            const imageData = context.getImageData(0, 0, this.width, this.height);
                            // 添加更细致的噪声
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 3) - 1;     // R
                                imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1; // G  
                                imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1; // B
                            }
                            context.putImageData(imageData, 0, 0);
                        } catch(e) {}
                    }
                    return originalToDataURL.apply(this, arguments);
                };
            }
            
            // AudioContext 指纹干扰
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (AudioContext) {
                const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                AudioContext.prototype.createAnalyser = function() {
                    const analyser = originalCreateAnalyser.call(this);
                    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                    analyser.getFloatFrequencyData = function(array) {
                        originalGetFloatFrequencyData.call(this, array);
                        for (let i = 0; i < array.length; i++) {
                            array[i] += Math.random() * 0.0001 - 0.00005;
                        }
                    };
                    return analyser;
                };
            }
            
            // 移除自动化痕迹（安全检查）
            if (window.chrome && window.chrome.runtime) {
                try {
                    delete window.chrome.runtime.onConnect;
                    delete window.chrome.runtime.onMessage;
                } catch(e) {}
            }
        } catch(e) {
            console.log('Enhanced anti-detection script error:', e);
        }
        """
        
        try:
            self.tab.run_js(enhanced_js)
            logging.info("增强反检测脚本应用成功")
        except Exception as e:
            logging.error(f"增强反检测脚本应用失败: {e}")
    
    def _simulate_enhanced_human_behavior(self):
        """模拟增强的真实用户行为"""
        try:
            if not self.tab:
                return
                
            # 随机鼠标移动
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 1820)
                y = random.randint(100, 980)
                self.tab.actions.move_to((x, y))
                time.sleep(random.uniform(0.2, 0.5))
            
            # 随机滚动
            scroll_amount = random.randint(-300, 300)
            scroll_js = f"window.scrollBy(0, {scroll_amount});"
            self.tab.run_js(scroll_js)
            
            # 更真实的等待时间
            time.sleep(random.uniform(1.5, 4.0))
            
            logging.info("增强用户行为模拟完成")
        except Exception as e:
            logging.error(f"用户行为模拟失败: {e}")
    
    def start_interactive_session(self):
        """启动交互式会话"""
        try:
            logging.info(f"启动交互式增强版注册会话，邮箱: {self.email}")
            
            # 初始化增强浏览器
            if not self.init_enhanced_browser():
                return False
            
            # 访问页面
            self.tab.get('https://augmentcode.com/')
            logging.info("页面加载完成")
            
            # 模拟增强用户行为
            self._simulate_enhanced_human_behavior()
            
            # 重新应用反检测脚本
            self._apply_enhanced_anti_detection()
            
            logging.info("=" * 60)
            logging.info("🚀 增强版浏览器已准备就绪！")
            logging.info("📋 当前配置:")
            logging.info("   - 美国地区指纹已应用")
            logging.info("   - Canvas/AudioContext 噪声注入已启用")
            logging.info("   - Turnstile 扩展已加载")
            logging.info("   - 代理已配置")
            logging.info("=" * 60)
            logging.info("💡 您现在可以:")
            logging.info("   1. 在浏览器中手动进行注册测试")
            logging.info("   2. 观察是否能通过地区限制检测")
            logging.info("   3. 按 Ctrl+C 结束会话")
            logging.info("=" * 60)
            
            # 保持会话运行
            try:
                while self.browser_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logging.info("收到中断信号，准备退出...")
                
            return True
            
        except Exception as e:
            logging.error(f"交互式会话启动失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            self.browser_running = False
            if hasattr(self.anti_detection_manager, 'browser') and self.anti_detection_manager.browser:
                self.anti_detection_manager.browser.quit()
            logging.info("增强版浏览器资源清理完成")
        except Exception as e:
            logging.error(f"清理资源失败: {e}")

# 使用示例
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        email = sys.argv[1]
    else:
        email = "<EMAIL>"
    
    register = InteractiveEnhancedRegister(email)
    
    try:
        success = register.start_interactive_session()
        if success:
            logging.info("交互式会话结束")
        else:
            logging.error("交互式会话启动失败")
    finally:
        register.cleanup()
