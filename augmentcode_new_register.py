#!/usr/bin/env python3
"""
基于新流程的 AugmentCode 完整注册脚本
按照用户要求的16步流程实现
"""

import os
import sys
import time
from logger import logging
from augmentcode_register import generate_email_with_timestamp

class AugmentCodeNewRegister:
    """新的 AugmentCode 注册器"""

    def __init__(self, email: str, browser_type: str = "chrome"):
        """
        初始化注册器

        Args:
            email: 注册邮箱
            browser_type: 浏览器类型 ("chrome", "edge", "chromium")
        """
        self.email = email
        self.browser = None
        self.tab = None
        self.browser_manager = None
        self.browser_type = browser_type
    
    def init_browser(self) -> bool:
        """
        初始化浏览器（使用 Cursor 的成功配置）
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logging.info(f"🚀 初始化 {self.browser_type.upper()} 浏览器...")

            from browser_utils import BrowserManager

            self.browser_manager = BrowserManager(browser_type=self.browser_type)
            self.browser = self.browser_manager.init_browser()
            self.tab = self.browser.latest_tab
            
            logging.info("✅ 浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def step1_visit_blog_page(self) -> bool:
        """
        第1步：打开 https://www.augmentcode.com/blog
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第1步：访问 AugmentCode Blog 页面...")
            
            self.tab.get("https://www.augmentcode.com/blog")
            time.sleep(3)
            
            # 截图记录
            self.tab.get_screenshot(path=f"step1_blog_page_{int(time.time())}.png")
            
            page_title = self.tab.title
            logging.info(f"📄 页面标题: {page_title}")
            
            logging.info("✅ 第1步完成：成功访问Blog页面")
            return True
            
        except Exception as e:
            logging.error(f"❌ 第1步失败: {e}")
            return False
    
    def step2_click_install_button(self) -> bool:
        """
        第2步：点击 install按钮 (/html/body/nav/div/div[2]/a[2])
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第2步：查找并点击Install按钮...")
            
            # 使用用户提供的xpath
            install_button = self.tab.ele("xpath:/html/body/nav/div/div[2]/a[2]", timeout=5)
            
            if not install_button:
                # 备用方法：通过href="/signup"查找
                install_button = self.tab.ele("@href=/signup", timeout=3)
            
            if not install_button:
                # 备用方法：通过文本查找
                install_button = self.tab.ele("@text()=Install", timeout=3)
            
            if not install_button:
                logging.error("❌ 未找到Install按钮")
                return False
            
            logging.info("✅ 找到Install按钮，准备点击")
            install_button.click()
            
            # 等待页面跳转
            time.sleep(5)
            
            # 截图记录跳转后的页面
            self.tab.get_screenshot(path=f"step2_after_install_click_{int(time.time())}.png")
            
            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")
            
            # 检查是否跳转到正确页面
            if "login.augmentcode.com/u/login/identifier" in new_url:
                logging.info("✅ 成功跳转到登录页面")
            else:
                logging.warning(f"⚠️ 跳转URL不符合预期: {new_url}")
            
            logging.info("✅ 第2步完成：成功点击Install按钮")
            return True
            
        except Exception as e:
            logging.error(f"❌ 第2步失败: {e}")
            return False
    
    def step3_handle_login_and_verification(self) -> bool:
        """
        第3步：处理登录页面，输入邮箱，等待人机验证
        
        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第3步：处理登录页面和人机验证...")
            
            # 等待页面完全加载
            time.sleep(3)
            
            # 处理人机验证（使用原有的方法）
            if not self._handle_turnstile_verification():
                logging.error("❌ 人机验证处理失败")
                return False
            
            # 查找邮箱输入框 (id="username")
            email_input = self.tab.ele("#username", timeout=10)
            
            if not email_input:
                logging.error("❌ 未找到邮箱输入框")
                return False
            
            # 输入邮箱
            email_input.clear()
            email_input.input(self.email)
            
            logging.info(f"✅ 邮箱输入成功: {self.email}")
            
            # 等待Success!出现
            success_found = False
            for i in range(30):  # 等待最多30秒
                try:
                    success_element = self.tab.ele("@text()=Success!", timeout=1)
                    if success_element:
                        logging.info("🎉 检测到 Success! 文本")
                        success_found = True
                        break
                except:
                    pass
                time.sleep(1)
                logging.debug(f"等待Success!出现... ({i+1}/30)")
            
            if not success_found:
                logging.warning("⚠️ 未检测到Success!文本，但继续流程")
            
            # 截图记录
            self.tab.get_screenshot(path=f"step3_login_complete_{int(time.time())}.png")
            
            logging.info("✅ 第3步完成：登录和验证处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第3步失败: {e}")
            return False

    def step4_click_continue_and_handle_verification_code(self) -> bool:
        """
        第4步：点击Continue，等待验证码，输入验证码，点击Continue

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第4步：点击Continue并处理验证码...")

            # 查找并点击Continue按钮
            continue_button = self.tab.ele("@text()=Continue", timeout=5)
            if not continue_button:
                logging.error("❌ 未找到Continue按钮")
                return False

            continue_button.click()
            logging.info("✅ 成功点击Continue按钮")

            # 等待页面跳转到验证码页面
            time.sleep(5)

            # 等待验证码邮件发送
            logging.info("⏳ 等待验证码邮件发送...")
            time.sleep(10)

            # 获取验证码
            try:
                from augmentcode_register import AugmentCodeRegister
                temp_register = AugmentCodeRegister(self.email)
                verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                if not verification_code:
                    logging.error("❌ 未获取到验证码")
                    return False

                logging.info(f"✅ 获取到验证码: {verification_code}")

            except Exception as e:
                logging.error(f"❌ 获取验证码失败: {e}")
                return False

            # 查找验证码输入框
            code_input = self.tab.ele("#code", timeout=10)
            if not code_input:
                # 备用选择器
                code_input = self.tab.ele("input[name=code]", timeout=5)

            if not code_input:
                logging.error("❌ 未找到验证码输入框")
                return False

            # 输入验证码
            code_input.clear()
            code_input.input(verification_code)

            logging.info("✅ 验证码输入成功")
            time.sleep(2)

            # 点击Continue提交验证码
            submit_button = self.tab.ele("@text()=Continue", timeout=5)
            if submit_button:
                submit_button.click()
                logging.info("✅ 验证码提交成功")
                time.sleep(5)
            else:
                logging.warning("⚠️ 未找到提交按钮，可能自动提交")

            # 截图记录
            self.tab.get_screenshot(path=f"step4_verification_complete_{int(time.time())}.png")

            logging.info("✅ 第4步完成：验证码处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第4步失败: {e}")
            return False

    def step5_wait_for_registration_success_page(self) -> bool:
        """
        第5步：等待页面加载，正确会进入 https://www.augmentcode.com/registration?status=success 页面

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第5步：等待页面加载到registration success页面...")

            # 等待页面跳转
            time.sleep(5)

            # 检查当前URL
            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 检查是否跳转到正确页面
            if "registration?status=success" in current_url:
                logging.info("✅ 成功进入registration success页面")
            else:
                logging.warning(f"⚠️ 当前页面不是预期的registration success页面: {current_url}")
                # 继续流程，可能页面结构有变化

            # 截图记录
            self.tab.get_screenshot(path=f"step5_registration_success_{int(time.time())}.png")

            logging.info("✅ 第5步完成：页面加载成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第5步失败: {e}")
            return False

    def step6_visit_promotions_cursor_and_login(self) -> bool:
        """
        第6步：打开 https://app.augmentcode.com/promotions/cursor，重复登录流程

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第6步：访问promotions/cursor页面并重复登录...")

            # 访问promotions页面
            self.tab.get("https://app.augmentcode.com/promotions/cursor")
            time.sleep(3)

            # 截图记录
            self.tab.get_screenshot(path=f"step6_promotions_page_{int(time.time())}.png")

            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 如果跳转到登录界面，重复步骤3、4
            if "login.augmentcode.com" in current_url:
                logging.info("🔍 检测到登录界面，开始重复登录流程...")

                # 重复步骤3：处理人机验证和输入邮箱
                if not self._handle_turnstile_verification():
                    logging.error("❌ 人机验证处理失败")
                    return False

                # 输入邮箱
                email_input = self.tab.ele("#username", timeout=10)
                if email_input:
                    email_input.clear()
                    email_input.input(self.email)
                    logging.info(f"✅ 邮箱输入成功: {self.email}")

                # 等待Success!
                success_found = False
                for i in range(30):
                    try:
                        success_element = self.tab.ele("@text()=Success!", timeout=1)
                        if success_element:
                            logging.info("🎉 检测到 Success! 文本")
                            success_found = True
                            break
                    except:
                        pass
                    time.sleep(1)

                # 点击Continue
                continue_button = self.tab.ele("@text()=Continue", timeout=5)
                if continue_button:
                    continue_button.click()
                    logging.info("✅ 成功点击Continue按钮")
                    time.sleep(5)

                # 重复步骤4：处理验证码
                logging.info("⏳ 等待验证码邮件发送...")
                time.sleep(10)

                # 获取验证码
                try:
                    from augmentcode_register import AugmentCodeRegister
                    temp_register = AugmentCodeRegister(self.email)
                    verification_code = temp_register.email_handler.get_augmentcode_verification_code(max_retries=5, retry_interval=30)

                    if verification_code:
                        logging.info(f"✅ 获取到验证码: {verification_code}")

                        # 输入验证码
                        code_input = self.tab.ele("#code", timeout=10)
                        if code_input:
                            code_input.clear()
                            code_input.input(verification_code)
                            logging.info("✅ 验证码输入成功")

                            # 点击Continue
                            submit_button = self.tab.ele("@text()=Continue", timeout=5)
                            if submit_button:
                                submit_button.click()
                                logging.info("✅ 验证码提交成功")
                                time.sleep(5)

                except Exception as e:
                    logging.error(f"❌ 获取验证码失败: {e}")
                    return False

            # 截图记录最终状态
            self.tab.get_screenshot(path=f"step6_login_complete_{int(time.time())}.png")

            logging.info("✅ 第6步完成：promotions页面访问和登录成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第6步失败: {e}")
            return False

    def step7_agree_terms_checkbox(self) -> bool:
        """
        第7步：勾选条款同意复选框 (//*[@id="terms-of-service-checkbox"])

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第7步：勾选条款同意复选框...")

            # 等待页面加载
            time.sleep(3)

            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 检查是否在terms-accept页面
            if "terms-accept" not in current_url:
                logging.info("ℹ️ 未在条款同意页面，可能已跳过")
                return True

            # 查找条款复选框
            terms_checkbox = self.tab.ele("#terms-of-service-checkbox", timeout=10)
            if not terms_checkbox:
                logging.error("❌ 未找到条款同意复选框")
                return False

            # 检查是否已勾选
            is_checked = terms_checkbox.attr("checked") is not None
            logging.info(f"📋 条款复选框当前状态: {'已勾选' if is_checked else '未勾选'}")

            if not is_checked:
                logging.info("☑️ 勾选条款同意复选框...")
                terms_checkbox.click()
                time.sleep(1)

                # 验证勾选成功
                is_checked_after = terms_checkbox.attr("checked") is not None
                if is_checked_after:
                    logging.info("✅ 条款复选框勾选成功")
                else:
                    # 使用JavaScript强制勾选
                    self.tab.run_js("""
                        var checkbox = document.getElementById('terms-of-service-checkbox');
                        if (checkbox) {
                            checkbox.checked = true;
                            var event = new Event('change', { bubbles: true });
                            checkbox.dispatchEvent(event);
                        }
                    """)
                    time.sleep(1)
                    logging.info("✅ 使用JavaScript勾选成功")
            else:
                logging.info("ℹ️ 条款复选框已勾选")

            # 截图记录
            self.tab.get_screenshot(path=f"step7_terms_checked_{int(time.time())}.png")

            logging.info("✅ 第7步完成：条款同意复选框处理成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第7步失败: {e}")
            return False

    def step8_click_continue_button(self) -> bool:
        """
        第8步：点击Continue按钮 (//*[@id="action-form"]/div[2]/button)

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第8步：点击Continue按钮...")

            # 使用用户提供的xpath
            continue_button = self.tab.ele("xpath://*[@id='action-form']/div[2]/button", timeout=5)

            if not continue_button:
                # 备用方法：通过文本查找
                continue_button = self.tab.ele("@text()=Continue", timeout=5)

            if not continue_button:
                logging.error("❌ 未找到Continue按钮")
                return False

            # 检查按钮是否可用
            disabled_attr = continue_button.attr("disabled")
            is_disabled = disabled_attr is not None
            logging.info(f"🔍 Continue按钮状态: {'禁用' if is_disabled else '启用'}")

            if is_disabled:
                # 等待按钮启用或强制启用
                logging.info("⏳ 等待按钮启用...")
                for i in range(10):
                    time.sleep(1)
                    disabled_attr = continue_button.attr("disabled")
                    if disabled_attr is None:
                        logging.info(f"✅ 按钮在 {i+1} 秒后启用")
                        break

                # 如果仍然禁用，强制启用
                if continue_button.attr("disabled") is not None:
                    self.tab.run_js("""
                        var button = document.querySelector('#action-form div:nth-child(2) button');
                        if (button) {
                            button.removeAttribute('disabled');
                            button.disabled = false;
                        }
                    """)
                    time.sleep(1)

            # 点击按钮
            continue_button.click()
            logging.info("✅ 成功点击Continue按钮")

            # 等待页面跳转
            time.sleep(5)

            # 截图记录
            self.tab.get_screenshot(path=f"step8_continue_clicked_{int(time.time())}.png")

            new_url = self.tab.url
            logging.info(f"🌐 跳转后 URL: {new_url}")

            logging.info("✅ 第8步完成：Continue按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第8步失败: {e}")
            return False

    def step9_save_html_and_upload_pdf(self) -> bool:
        """
        第9步：保存当前页面HTML源码，上传WIPDF.pdf文件

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第9步：保存HTML源码并上传PDF文件...")

            # 等待页面完全加载
            time.sleep(5)

            current_url = self.tab.url
            logging.info(f"🔍 当前 URL: {current_url}")

            # 检查是否到达promotions/cursor页面
            if "app.augmentcode.com/promotions/cursor" not in current_url:
                logging.warning(f"⚠️ 当前页面不是预期的promotions页面: {current_url}")

            # 保存HTML源码
            try:
                html_content = self.tab.html
                html_filename = f"promotions_page_{int(time.time())}.html"
                with open(html_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                logging.info(f"✅ HTML源码已保存: {html_filename}")
            except Exception as e:
                logging.warning(f"⚠️ 保存HTML源码失败: {e}")

            # 调试：检查页面上所有的input元素
            try:
                all_inputs = self.tab.eles("tag:input")
                logging.info(f"🔍 页面上找到 {len(all_inputs)} 个input元素")
                for i, inp in enumerate(all_inputs):
                    inp_type = inp.attr("type") or "text"
                    inp_name = inp.attr("name") or "无名称"
                    inp_class = inp.attr("class") or "无class"
                    logging.info(f"  Input {i+1}: type={inp_type}, name={inp_name}, class={inp_class}")
            except Exception as e:
                logging.warning(f"⚠️ 调试input元素失败: {e}")

            # 查找文件上传输入框（隐藏的input）
            file_input = None

            # 尝试多种选择器
            selectors = [
                ".file-input",
                "input[type=file][name=invoice]",
                "input[type=file]",
                "input[name=invoice]"
            ]

            for selector in selectors:
                try:
                    file_input = self.tab.ele(selector, timeout=3)
                    if file_input:
                        logging.info(f"✅ 使用选择器 '{selector}' 找到文件输入框")
                        break
                except:
                    continue

            if not file_input:
                logging.error("❌ 未找到文件上传输入框")
                # 尝试点击拖拽区域来激活文件选择
                drag_drop_zone = self.tab.ele(".drag-drop-zone", timeout=5)
                if drag_drop_zone:
                    logging.info("🔍 找到拖拽区域，尝试点击激活文件选择...")
                    drag_drop_zone.click()
                    time.sleep(2)
                    # 重新查找文件输入框
                    for selector in selectors:
                        try:
                            file_input = self.tab.ele(selector, timeout=2)
                            if file_input:
                                logging.info(f"✅ 点击拖拽区域后，使用选择器 '{selector}' 找到文件输入框")
                                break
                        except:
                            continue

                if not file_input:
                    logging.warning("⚠️ 仍未找到文件上传输入框，跳过文件上传步骤")
                    # 暂时跳过文件上传，继续后续流程
                    logging.info("✅ 第9步完成：HTML保存成功（跳过PDF上传）")
                    return True

            # 检查PDF文件是否存在
            pdf_path = os.path.join(os.getcwd(), "WIPDF.pdf")
            if not os.path.exists(pdf_path):
                logging.error(f"❌ PDF文件不存在: {pdf_path}")
                return False

            logging.info(f"📁 找到PDF文件: {pdf_path}")

            # 上传PDF文件
            try:
                # 方法1：直接使用input方法
                file_input.input(pdf_path)
                logging.info("✅ PDF文件上传成功（方法1）")
            except Exception as e:
                logging.warning(f"⚠️ 方法1失败: {e}")
                try:
                    # 方法2：使用JavaScript设置文件
                    self.tab.run_js(f"""
                        var fileInput = document.querySelector('.file-input');
                        if (fileInput) {{
                            var file = new File([''], '{os.path.basename(pdf_path)}', {{type: 'application/pdf'}});
                            var dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            fileInput.files = dataTransfer.files;

                            // 触发change事件
                            var event = new Event('change', {{ bubbles: true }});
                            fileInput.dispatchEvent(event);
                        }}
                    """)
                    logging.info("✅ PDF文件上传成功（方法2）")
                except Exception as e2:
                    logging.error(f"❌ 方法2也失败: {e2}")
                    # 方法3：点击拖拽区域然后使用系统文件对话框
                    try:
                        drag_drop_zone = self.tab.ele(".drag-drop-zone", timeout=3)
                        if drag_drop_zone:
                            drag_drop_zone.click()
                            time.sleep(1)
                            # 重新尝试文件输入
                            file_input_new = self.tab.ele(".file-input", timeout=3)
                            if file_input_new:
                                file_input_new.input(pdf_path)
                                logging.info("✅ PDF文件上传成功（方法3）")
                    except Exception as e3:
                        logging.error(f"❌ 所有上传方法都失败: {e3}")
                        return False

            # 等待文件上传完成
            time.sleep(3)

            # 截图记录
            self.tab.get_screenshot(path=f"step9_pdf_uploaded_{int(time.time())}.png")

            logging.info("✅ 第9步完成：HTML保存和PDF上传成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第9步失败: {e}")
            return False

    def step10_click_upload_invoice(self) -> bool:
        """
        第10步：点击Upload Invoice按钮

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第10步：点击Upload Invoice按钮...")

            # 使用用户提供的xpath
            upload_button = self.tab.ele("xpath:/html/body/div[1]/div[1]/div/div[2]/div/div[2]/div/div/div[2]/button", timeout=10)

            if not upload_button:
                # 备用方法：通过文本查找
                upload_button = self.tab.ele("@text()=Upload Invoice", timeout=5)

            if not upload_button:
                # 备用方法：通过class查找
                upload_button = self.tab.ele(".rt-Button", timeout=5)

            if not upload_button:
                logging.error("❌ 未找到Upload Invoice按钮")
                return False

            logging.info("✅ 找到Upload Invoice按钮")

            # 点击按钮
            upload_button.click()
            logging.info("✅ 成功点击Upload Invoice按钮")

            # 等待处理结果
            time.sleep(5)

            # 截图记录
            self.tab.get_screenshot(path=f"step10_upload_clicked_{int(time.time())}.png")

            logging.info("✅ 第10步完成：Upload Invoice按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第10步失败: {e}")
            return False

    def step11_wait_for_welcome_benefit(self) -> bool:
        """
        第11步：等待页面出现Welcome Benefit Applied!文本

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第11步：等待Welcome Benefit Applied!文本出现...")

            # 等待Welcome Benefit Applied!文本出现
            welcome_found = False
            for i in range(60):  # 等待最多60秒
                try:
                    # 搜索整个页面的文本
                    page_text = self.tab.html.lower()
                    if "welcome benefit applied!" in page_text:
                        logging.info("🎉 检测到 Welcome Benefit Applied! 文本")
                        welcome_found = True
                        break

                    # 也尝试直接查找元素
                    welcome_element = self.tab.ele("@text()=Welcome Benefit Applied!", timeout=1)
                    if welcome_element:
                        logging.info("🎉 找到 Welcome Benefit Applied! 元素")
                        welcome_found = True
                        break

                except:
                    pass

                time.sleep(1)
                logging.debug(f"等待Welcome Benefit Applied!出现... ({i+1}/60)")

            if not welcome_found:
                logging.warning("⚠️ 未检测到Welcome Benefit Applied!文本，但继续流程")
                # 截图记录当前状态
                self.tab.get_screenshot(path=f"step11_no_welcome_text_{int(time.time())}.png")
            else:
                # 截图记录成功状态
                self.tab.get_screenshot(path=f"step11_welcome_benefit_found_{int(time.time())}.png")

            logging.info("✅ 第11步完成：Welcome Benefit检查完成")
            return True

        except Exception as e:
            logging.error(f"❌ 第11步失败: {e}")
            return False

    def step12_visit_subscription_and_change_plan(self) -> bool:
        """
        第12步：进入subscription页面，点击Change plan按钮

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第12步：访问subscription页面并点击Change plan...")

            # 访问subscription页面
            self.tab.get("https://app.augmentcode.com/account/subscription")
            time.sleep(3)

            # 截图记录
            self.tab.get_screenshot(path=f"step12_subscription_page_{int(time.time())}.png")

            # 查找Change plan按钮
            change_plan_button = self.tab.ele("@text()=Change plan", timeout=10)

            if not change_plan_button:
                # 备用方法：通过class查找
                change_plan_button = self.tab.ele(".rt-Button", timeout=5)

            if not change_plan_button:
                logging.error("❌ 未找到Change plan按钮")
                return False

            logging.info("✅ 找到Change plan按钮")

            # 点击按钮
            change_plan_button.click()
            logging.info("✅ 成功点击Change plan按钮")

            # 等待页面响应
            time.sleep(3)

            # 截图记录
            self.tab.get_screenshot(path=f"step12_change_plan_clicked_{int(time.time())}.png")

            logging.info("✅ 第12步完成：Change plan按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第12步失败: {e}")
            return False

    def step13_click_community_plan(self) -> bool:
        """
        第13步：点击包含文本Community Plan的元素

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第13步：点击Community Plan...")

            # 等待2秒（按用户要求）
            time.sleep(2)

            # 查找包含Community Plan文本且class包含plan-option-card clickable的元素
            community_plan = self.tab.ele(".plan-option-card.clickable", timeout=10)

            if not community_plan:
                # 备用方法：直接通过文本查找
                community_plan = self.tab.ele("@text()*=Community Plan", timeout=5)

            if not community_plan:
                logging.error("❌ 未找到Community Plan元素")
                return False

            logging.info("✅ 找到Community Plan元素")

            # 点击元素
            community_plan.click()
            logging.info("✅ 成功点击Community Plan")

            # 等待页面响应
            time.sleep(2)

            # 截图记录
            self.tab.get_screenshot(path=f"step13_community_plan_clicked_{int(time.time())}.png")

            logging.info("✅ 第13步完成：Community Plan点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第13步失败: {e}")
            return False

    def step14_click_confirmation_text(self) -> bool:
        """
        第14步：点击id="confirmation-text"的元素

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第14步：点击confirmation-text元素...")

            # 查找confirmation-text元素
            confirmation_text = self.tab.ele("#confirmation-text", timeout=10)

            if not confirmation_text:
                logging.error("❌ 未找到confirmation-text元素")
                return False

            logging.info("✅ 找到confirmation-text元素")

            # 点击元素
            confirmation_text.click()
            logging.info("✅ 成功点击confirmation-text")

            # 等待页面响应
            time.sleep(1)

            # 截图记录
            self.tab.get_screenshot(path=f"step14_confirmation_clicked_{int(time.time())}.png")

            logging.info("✅ 第14步完成：confirmation-text点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第14步失败: {e}")
            return False

    def step15_click_select_plan(self) -> bool:
        """
        第15步：点击Select Plan按钮

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第15步：点击Select Plan按钮...")

            # 查找Select Plan按钮
            select_plan_button = self.tab.ele(".plan-select-button", timeout=10)

            if not select_plan_button:
                # 备用方法：通过文本查找
                select_plan_button = self.tab.ele("@text()=Select Plan", timeout=5)

            if not select_plan_button:
                logging.error("❌ 未找到Select Plan按钮")
                return False

            logging.info("✅ 找到Select Plan按钮")

            # 点击按钮
            select_plan_button.click()
            logging.info("✅ 成功点击Select Plan按钮")

            # 截图记录
            self.tab.get_screenshot(path=f"step15_select_plan_clicked_{int(time.time())}.png")

            logging.info("✅ 第15步完成：Select Plan按钮点击成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第15步失败: {e}")
            return False

    def step16_wait_and_final_screenshot(self) -> bool:
        """
        第16步：等待10秒，刷新页面，截图保存

        Returns:
            bool: 操作是否成功
        """
        try:
            logging.info("📋 第16步：等待10秒，刷新页面并截图...")

            # 等待10秒
            logging.info("⏳ 等待10秒...")
            time.sleep(10)

            # 刷新当前页面
            self.tab.refresh()
            logging.info("🔄 页面已刷新")

            # 等待页面加载
            time.sleep(3)

            # 截图保存，使用邮箱名字作为文件名
            email_name = self.email.split('@')[0]  # 获取邮箱@前面的部分
            screenshot_filename = f"{email_name}_final_result.png"
            self.tab.get_screenshot(path=screenshot_filename)

            logging.info(f"📸 最终截图已保存: {screenshot_filename}")

            # 记录最终状态
            final_url = self.tab.url
            final_title = self.tab.title
            logging.info(f"🌐 最终 URL: {final_url}")
            logging.info(f"📄 最终标题: {final_title}")

            logging.info("✅ 第16步完成：等待、刷新和截图成功")
            return True

        except Exception as e:
            logging.error(f"❌ 第16步失败: {e}")
            return False

    def _handle_turnstile_verification(self) -> bool:
        """
        处理Turnstile人机验证（复用原有方法）

        Returns:
            bool: 验证是否成功
        """
        try:
            logging.info("🔒 开始处理Turnstile人机验证...")

            # 设置日语语言环境（提高验证成功率）
            self._setup_japanese_language()

            # 检查是否已经验证成功
            if self._check_turnstile_success():
                logging.info("🎉 Turnstile验证已完成！")
                return True

            # 尝试点击验证框
            max_retries = 3
            for retry in range(max_retries):
                logging.info(f"🔄 Turnstile验证尝试 {retry+1}/{max_retries}")

                try:
                    # 查找验证框
                    challenge_check = None

                    # 方法1: Auth0 V2 Captcha
                    try:
                        challenge_check = (
                            self.tab.ele("#ulp-auth0-v2-captcha", timeout=3)
                            .child()
                            .shadow_root.ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input")
                        )
                    except:
                        pass

                    # 方法2: Cloudflare Turnstile
                    if not challenge_check:
                        try:
                            challenge_check = (
                                self.tab.ele("@id=cf-turnstile", timeout=3)
                                .child()
                                .shadow_root.ele("tag:iframe")
                                .ele("tag:body")
                                .sr("tag:input")
                            )
                        except:
                            pass

                    if challenge_check:
                        logging.info("🖱️ 点击Turnstile验证框...")
                        challenge_check.click()
                        time.sleep(2)

                        # 检查验证是否成功
                        if self._check_turnstile_success():
                            logging.info("🎉 Turnstile验证成功！")
                            return True

                except Exception as e:
                    logging.debug(f"第 {retry+1} 次尝试失败: {e}")

                # 检查是否已经验证成功
                if self._check_turnstile_success():
                    logging.info("🎉 Turnstile验证已完成！")
                    return True

                if retry < max_retries - 1:
                    time.sleep(2)

            logging.warning("⚠️ Turnstile验证未能确认成功，但继续流程")
            return True

        except Exception as e:
            logging.error(f"❌ Turnstile验证处理失败: {e}")
            return True  # 保守地继续流程

    def _setup_japanese_language(self):
        """设置日语语言环境以提高验证成功率"""
        try:
            logging.info("🌐 设置日语语言环境...")
            self.tab.run_js("""
                Object.defineProperty(navigator, 'language', {
                    get: function() { return 'ja-JP'; }
                });
                Object.defineProperty(navigator, 'languages', {
                    get: function() { return ['ja-JP', 'ja', 'en']; }
                });
            """)
            logging.info("✅ 日语语言环境设置完成")
        except Exception as e:
            logging.warning(f"⚠️ 设置日语语言环境失败: {e}")

    def _check_turnstile_success(self) -> bool:
        """检查Turnstile验证是否成功"""
        try:
            # 检查页面级别的成功标志
            page_indicators = [
                "@name=password",
                "@data-index=0",
                "Account Settings",
            ]

            for indicator in page_indicators:
                try:
                    element = self.tab.ele(indicator, timeout=1)
                    if element:
                        logging.info(f"✅ 检测到页面级验证成功标志: {indicator}")
                        return True
                except:
                    continue

            # 检查iframe内部的成功标志
            try:
                auth0_captcha = self.tab.ele("#ulp-auth0-v2-captcha", timeout=1)
                if auth0_captcha:
                    iframe_body = (
                        auth0_captcha
                        .child()
                        .shadow_root.ele("tag:iframe")
                        .ele("tag:body")
                    )

                    if iframe_body:
                        # 检查成功元素
                        try:
                            success_element = iframe_body.sr("#success")
                            if success_element and hasattr(success_element, 'text'):
                                if success_element.text and success_element.text.lower() == "success!":
                                    logging.info("✅ 检测到iframe内Success!元素")
                                    return True
                        except:
                            pass

                        # 检查challenge input值
                        try:
                            challenge_input = iframe_body.sr("tag:input")
                            if challenge_input:
                                input_value = challenge_input.attr("value")
                                if input_value and len(input_value) > 10:
                                    logging.info("✅ Challenge input值已生成，验证完成")
                                    return True
                        except:
                            pass
            except:
                pass

            return False

        except Exception as e:
            logging.debug(f"检查验证成功状态异常: {e}")
            return False

    def complete_registration(self) -> bool:
        """
        完成完整的注册流程

        Returns:
            bool: 注册是否成功
        """
        try:
            logging.info("🚀 开始新的 AugmentCode 注册流程...")

            # 初始化浏览器
            if not self.init_browser():
                return False

            # 执行16个注册步骤
            steps = [
                ("访问Blog页面", self.step1_visit_blog_page),
                ("点击Install按钮", self.step2_click_install_button),
                ("处理登录和人机验证", self.step3_handle_login_and_verification),
                ("点击Continue并处理验证码", self.step4_click_continue_and_handle_verification_code),
                ("等待registration success页面", self.step5_wait_for_registration_success_page),
                ("访问promotions页面并重复登录", self.step6_visit_promotions_cursor_and_login),
                ("勾选条款同意复选框", self.step7_agree_terms_checkbox),
                ("点击Continue按钮", self.step8_click_continue_button),
                ("保存HTML并上传PDF", self.step9_save_html_and_upload_pdf),
                ("点击Upload Invoice", self.step10_click_upload_invoice),
                ("等待Welcome Benefit", self.step11_wait_for_welcome_benefit),
                ("访问subscription并Change plan", self.step12_visit_subscription_and_change_plan),
                ("点击Community Plan", self.step13_click_community_plan),
                ("点击确认文本", self.step14_click_confirmation_text),
                ("点击Select Plan", self.step15_click_select_plan),
                ("等待、刷新和截图", self.step16_wait_and_final_screenshot),
            ]

            for step_name, step_func in steps:
                logging.info(f"\n{'='*20} {step_name} {'='*20}")

                if not step_func():
                    logging.error(f"❌ {step_name} 失败，注册中止")
                    return False

                logging.info(f"✅ {step_name} 成功")

            # 检查最终结果
            time.sleep(3)
            final_url = self.tab.url
            final_title = self.tab.title

            logging.info(f"🌐 最终 URL: {final_url}")
            logging.info(f"📄 最终标题: {final_title}")

            logging.info("🎉 所有步骤完成！")
            return True

        except Exception as e:
            logging.error(f"❌ 注册流程失败: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
            return False
        finally:
            # 保持浏览器打开供检查
            input("按 Enter 键关闭浏览器...")
            if self.browser_manager:
                self.browser_manager.quit()

def main():
    """主函数"""
    try:
        logging.info("=" * 80)
        logging.info("新的 AugmentCode 注册流程")
        logging.info("=" * 80)

        # 检查环境
        proxy = os.getenv("BROWSER_PROXY", "")
        if not proxy:
            logging.warning("⚠️ 建议设置代理: set BROWSER_PROXY=127.0.0.1:1080")

        # 生成邮箱
        email = generate_email_with_timestamp()
        logging.info(f"🎯 生成的邮箱: {email}")

        # 创建注册器
        register = AugmentCodeNewRegister(email)

        # 执行注册
        success = register.complete_registration()

        if success:
            logging.info("🎉 AugmentCode 注册成功！")
            print(f"\n✅ 注册成功！")
            print(f"📧 邮箱: {email}")
        else:
            logging.error("😞 AugmentCode 注册失败！")
            print(f"\n❌ 注册失败！")

        return success

    except Exception as e:
        logging.error(f"❌ 主程序执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
