#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监听AugmentCode文件上传事件的脚本
运行到上传页面，然后监听各种元素和事件
"""

import os
import sys
import time
import logging
from browser_utils import BrowserManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_monitor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class UploadMonitor:
    def __init__(self):
        self.tab = None
        self.browser = None
        self.browser_manager = None
        self.email = "<EMAIL>"
        
    def init_browser(self):
        """初始化浏览器"""
        try:
            logging.info("🚀 初始化浏览器...")
            
            self.browser_manager = BrowserManager(browser_type="chrome")
            self.browser = self.browser_manager.init_browser()
            self.tab = self.browser.latest_tab
            
            logging.info("✅ 浏览器初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def setup_event_listeners(self):
        """设置事件监听器"""
        try:
            logging.info("🔧 设置事件监听器...")
            
            # JavaScript代码来监听各种事件
            js_code = """
            // 创建全局监听器对象
            window.uploadMonitor = {
                events: [],
                logEvent: function(type, target, detail) {
                    const timestamp = new Date().toISOString();
                    const event = {
                        timestamp: timestamp,
                        type: type,
                        target: target.tagName + (target.className ? '.' + target.className.replace(/\\s+/g, '.') : ''),
                        detail: detail || ''
                    };
                    this.events.push(event);
                    console.log('🎯 事件监听:', event);
                }
            };
            
            // 监听文件输入框的change事件
            document.addEventListener('change', function(e) {
                if (e.target.type === 'file') {
                    window.uploadMonitor.logEvent('change', e.target, 
                        'files: ' + (e.target.files ? e.target.files.length : 0));
                }
            }, true);
            
            // 监听input事件
            document.addEventListener('input', function(e) {
                if (e.target.type === 'file') {
                    window.uploadMonitor.logEvent('input', e.target, 
                        'files: ' + (e.target.files ? e.target.files.length : 0));
                }
            }, true);
            
            // 监听点击事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('drag-drop-zone') || 
                    e.target.closest('.drag-drop-zone') ||
                    e.target.type === 'file') {
                    window.uploadMonitor.logEvent('click', e.target, '');
                }
            }, true);
            
            // 监听拖拽事件
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventType => {
                document.addEventListener(eventType, function(e) {
                    if (e.target.classList.contains('drag-drop-zone') || 
                        e.target.closest('.drag-drop-zone')) {
                        window.uploadMonitor.logEvent(eventType, e.target, '');
                    }
                }, true);
            });
            
            // 监听按钮状态变化
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'disabled' &&
                        mutation.target.textContent.includes('Upload')) {
                        window.uploadMonitor.logEvent('button-state-change', mutation.target, 
                            'disabled: ' + mutation.target.disabled);
                    }
                });
            });
            
            // 观察上传按钮
            const uploadButton = document.querySelector('button:contains("Upload")') || 
                                document.querySelector('[data-disabled]');
            if (uploadButton) {
                observer.observe(uploadButton, { attributes: true });
            }
            
            console.log('✅ 事件监听器设置完成');
            return true;
            """
            
            result = self.tab.run_js(js_code)
            logging.info("✅ 事件监听器设置完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 设置事件监听器失败: {e}")
            return False
    
    def analyze_current_page(self):
        """分析当前页面状态"""
        try:
            logging.info("🔍 分析当前页面状态...")
            
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")
            
            # 查找文件输入框
            file_inputs = self.tab.eles('input[type=file]')
            logging.info(f"📁 找到 {len(file_inputs)} 个文件输入框")
            for i, inp in enumerate(file_inputs):
                name = inp.attr('name') or '无名称'
                accept = inp.attr('accept') or '无限制'
                class_name = inp.attr('class') or '无class'
                style = inp.attr('style') or '无style'
                logging.info(f"  文件输入框 {i+1}: name={name}, accept={accept}, class={class_name}")
                if 'display: none' in style or 'file-input' in class_name:
                    logging.info(f"    ⚠️ 此输入框被隐藏")
            
            # 查找拖拽区域
            drag_zones = self.tab.eles('.drag-drop-zone')
            logging.info(f"🎯 找到 {len(drag_zones)} 个拖拽区域")
            for i, zone in enumerate(drag_zones):
                class_name = zone.attr('class') or '无class'
                role = zone.attr('role') or '无role'
                aria_label = zone.attr('aria-label') or '无aria-label'
                logging.info(f"  拖拽区域 {i+1}: class={class_name}, role={role}, aria-label={aria_label}")
            
            # 查找上传按钮
            upload_buttons = self.tab.eles('button')
            upload_btn_count = 0
            for btn in upload_buttons:
                btn_text = btn.text.strip()
                if 'upload' in btn_text.lower():
                    upload_btn_count += 1
                    disabled = btn.attr('disabled')
                    data_disabled = btn.attr('data-disabled')
                    logging.info(f"  上传按钮: text='{btn_text}', disabled={disabled}, data-disabled={data_disabled}")
            
            logging.info(f"🔘 找到 {upload_btn_count} 个上传相关按钮")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 分析页面失败: {e}")
            return False
    
    def get_monitored_events(self):
        """获取监听到的事件"""
        try:
            js_code = """
            return window.uploadMonitor ? window.uploadMonitor.events : [];
            """
            events = self.tab.run_js(js_code)
            return events or []
        except:
            return []
    
    def start_monitoring(self):
        """开始监听"""
        try:
            logging.info("🚀 开始文件上传监听...")
            
            # 1. 初始化浏览器
            if not self.init_browser():
                return False
            
            # 2. 提示用户手动导航
            logging.info("=" * 60)
            logging.info("📋 请按以下步骤操作：")
            logging.info("1. 手动登录到 https://app.augmentcode.com/promotions/cursor")
            logging.info("2. 到达文件上传页面后，按Enter键继续")
            logging.info("=" * 60)
            input("按Enter键继续...")
            
            # 3. 分析当前页面
            if not self.analyze_current_page():
                return False
            
            # 4. 设置事件监听器
            if not self.setup_event_listeners():
                return False
            
            # 5. 开始监听循环
            logging.info("=" * 60)
            logging.info("🎯 监听已开始！请进行文件上传操作...")
            logging.info("📝 我将实时显示监听到的事件")
            logging.info("⌨️ 完成上传测试后，按Enter键查看完整事件日志")
            logging.info("=" * 60)
            
            # 实时监听事件
            last_event_count = 0
            monitoring_start = time.time()

            while True:
                try:
                    events = self.get_monitored_events()
                    new_events = events[last_event_count:]

                    for event in new_events:
                        logging.info(f"🎯 {event['timestamp']} - {event['type']} on {event['target']} {event['detail']}")

                    last_event_count = len(events)

                    # 每30秒提示一次状态
                    if int(time.time() - monitoring_start) % 30 == 0 and int(time.time() - monitoring_start) > 0:
                        logging.info(f"⏰ 监听中... 已监听 {int(time.time() - monitoring_start)} 秒，捕获 {len(events)} 个事件")
                        time.sleep(1)  # 避免重复提示

                    time.sleep(0.5)  # 短暂休眠

                except KeyboardInterrupt:
                    logging.info("⌨️ 收到中断信号，停止监听...")
                    break
                except Exception as e:
                    logging.warning(f"⚠️ 监听过程中出错: {e}")
                    time.sleep(0.5)
                    continue
            
            # 6. 显示完整事件日志
            logging.info("=" * 60)
            logging.info("📊 完整事件日志：")
            logging.info("=" * 60)
            
            final_events = self.get_monitored_events()
            for i, event in enumerate(final_events):
                logging.info(f"{i+1:2d}. {event['timestamp']} - {event['type']} on {event['target']} {event['detail']}")
            
            if not final_events:
                logging.warning("⚠️ 没有监听到任何事件")
            
            logging.info("=" * 60)
            logging.info("✅ 监听完成")
            
            # 保持浏览器打开
            input("按Enter键关闭浏览器...")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 监听失败: {e}")
            return False
        finally:
            if self.tab:
                try:
                    self.tab.close()
                except:
                    pass

if __name__ == "__main__":
    monitor = UploadMonitor()
    success = monitor.start_monitoring()
    
    if success:
        print("✅ 监听成功完成")
    else:
        print("❌ 监听失败")
