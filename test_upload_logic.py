#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AugmentCode文件上传逻辑的专用脚本
从第9步开始，探索正确的文件上传方法
"""

import os
import sys
import time
import logging
import base64

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_upload.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class UploadTester:
    def __init__(self):
        self.tab = None
        self.browser = None
        self.browser_manager = None
        self.email = "<EMAIL>"
        
    def init_browser(self):
        """初始化浏览器（复用注册脚本的配置）"""
        try:
            logging.info("🚀 初始化浏览器...")

            from browser_utils import BrowserManager

            self.browser_manager = BrowserManager(browser_type="chrome")
            self.browser = self.browser_manager.init_browser()
            self.tab = self.browser.latest_tab

            logging.info("✅ 浏览器初始化成功")
            return True

        except Exception as e:
            logging.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def navigate_to_promotions_page(self):
        """导航到promotions页面"""
        try:
            logging.info("🔍 尝试直接访问promotions页面...")

            # 直接访问promotions页面
            self.tab.get("https://app.augmentcode.com/promotions/cursor")
            time.sleep(8)  # 等待页面加载和可能的重定向

            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")

            # 如果被重定向到登录页面，进行简单登录
            if "login" in current_url.lower():
                logging.info("🔐 检测到需要登录，进行登录...")

                # 查找邮箱输入框
                email_input = self.tab.ele('#email', timeout=10)
                if not email_input:
                    email_input = self.tab.ele('input[type=email]', timeout=5)

                if email_input:
                    email_input.clear()
                    email_input.input(self.email)
                    logging.info(f"✅ 输入邮箱: {self.email}")

                    # 查找并点击Continue按钮
                    continue_btn = self.tab.ele('button:contains("Continue")', timeout=10)
                    if not continue_btn:
                        continue_btn = self.tab.ele('button[type=submit]', timeout=5)

                    if continue_btn:
                        continue_btn.click()
                        logging.info("✅ 点击Continue按钮")
                        time.sleep(5)

                        # 再次尝试访问promotions页面
                        self.tab.get("https://app.augmentcode.com/promotions/cursor")
                        time.sleep(5)

                        current_url = self.tab.url
                        logging.info(f"🔍 登录后URL: {current_url}")

            # 检查是否成功到达promotions页面
            if "promotions" in current_url and "cursor" in current_url:
                logging.info("✅ 成功到达promotions页面")
                return True
            else:
                logging.warning(f"⚠️ 未到达预期页面，但继续测试: {current_url}")
                # 即使没有到达预期页面，也继续测试，可能页面结构相似
                return True

        except Exception as e:
            logging.error(f"❌ 导航失败: {e}")
            return False
    
    def analyze_upload_elements(self):
        """分析页面上的上传相关元素"""
        try:
            logging.info("🔍 分析页面上传元素...")
            
            # 保存HTML源码
            html_content = self.tab.html
            timestamp = int(time.time())
            html_filename = f"upload_test_page_{timestamp}.html"
            
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logging.info(f"✅ HTML源码已保存: {html_filename}")
            
            # 查找所有input元素
            all_inputs = self.tab.eles("tag:input")
            logging.info(f"🔍 页面上找到 {len(all_inputs)} 个input元素")
            for i, inp in enumerate(all_inputs):
                inp_type = inp.attr("type") or "text"
                inp_name = inp.attr("name") or "无名称"
                inp_class = inp.attr("class") or "无class"
                inp_style = inp.attr("style") or "无style"
                logging.info(f"  Input {i+1}: type={inp_type}, name={inp_name}, class={inp_class}, style={inp_style}")
            
            # 查找拖拽区域
            drag_zones = self.tab.eles(".drag-drop-zone")
            logging.info(f"🔍 找到 {len(drag_zones)} 个拖拽区域")
            for i, zone in enumerate(drag_zones):
                zone_class = zone.attr("class") or "无class"
                zone_role = zone.attr("role") or "无role"
                zone_aria = zone.attr("aria-label") or "无aria-label"
                logging.info(f"  拖拽区域 {i+1}: class={zone_class}, role={zone_role}, aria-label={zone_aria}")
            
            # 查找上传按钮
            upload_buttons = self.tab.eles('button:contains("Upload")')
            logging.info(f"🔍 找到 {len(upload_buttons)} 个上传按钮")
            for i, btn in enumerate(upload_buttons):
                btn_text = btn.text
                btn_disabled = btn.attr("disabled")
                btn_class = btn.attr("class") or "无class"
                logging.info(f"  按钮 {i+1}: text={btn_text}, disabled={btn_disabled}, class={btn_class}")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 分析元素失败: {e}")
            return False
    
    def test_file_upload_methods(self):
        """测试不同的文件上传方法"""
        try:
            logging.info("🧪 开始测试文件上传方法...")
            
            # 检查PDF文件
            pdf_path = os.path.abspath("WIPDF.pdf")
            if not os.path.exists(pdf_path):
                logging.error(f"❌ PDF文件不存在: {pdf_path}")
                return False
            
            logging.info(f"📁 找到PDF文件: {pdf_path}")
            
            # 方法1：直接查找并操作文件输入框
            logging.info("🧪 方法1：直接操作文件输入框")
            file_input = self.tab.ele('input[type=file][name=invoice]', timeout=3)
            if file_input:
                logging.info("✅ 找到文件输入框")
                try:
                    file_input.input(pdf_path)
                    logging.info("✅ 方法1成功：直接input文件")
                    time.sleep(2)
                    self.check_upload_button_status()
                    return True
                except Exception as e:
                    logging.warning(f"⚠️ 方法1失败: {e}")
            else:
                logging.warning("⚠️ 方法1：未找到文件输入框")
            
            # 方法2：点击拖拽区域后操作文件输入框
            logging.info("🧪 方法2：点击拖拽区域后操作文件输入框")
            drag_zone = self.tab.ele('.drag-drop-zone', timeout=3)
            if drag_zone:
                logging.info("✅ 找到拖拽区域")
                drag_zone.click()
                time.sleep(1)
                
                file_input = self.tab.ele('input[type=file][name=invoice]', timeout=3)
                if file_input:
                    try:
                        file_input.input(pdf_path)
                        logging.info("✅ 方法2成功：点击拖拽区域后input文件")
                        time.sleep(2)
                        self.check_upload_button_status()
                        return True
                    except Exception as e:
                        logging.warning(f"⚠️ 方法2失败: {e}")
                else:
                    logging.warning("⚠️ 方法2：点击后仍未找到文件输入框")
            else:
                logging.warning("⚠️ 方法2：未找到拖拽区域")
            
            # 方法3：使用JavaScript设置文件
            logging.info("🧪 方法3：使用JavaScript设置文件")
            try:
                # 读取PDF文件内容
                with open(pdf_path, 'rb') as f:
                    pdf_content = f.read()
                
                # 转换为base64
                pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
                
                js_code = f"""
                // 将base64转换为Blob
                const base64Data = '{pdf_base64}';
                const byteCharacters = atob(base64Data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {{
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }}
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], {{type: 'application/pdf'}});
                
                // 创建File对象
                const file = new File([blob], '{os.path.basename(pdf_path)}', {{
                    type: 'application/pdf',
                    lastModified: Date.now()
                }});
                
                // 创建DataTransfer对象并添加文件
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                
                // 设置到文件输入框
                const fileInput = document.querySelector('input[type=file][name=invoice]');
                if (fileInput) {{
                    fileInput.files = dataTransfer.files;
                    
                    // 触发各种事件
                    fileInput.dispatchEvent(new Event('change', {{bubbles: true}}));
                    fileInput.dispatchEvent(new Event('input', {{bubbles: true}}));
                    
                    console.log('文件已设置，文件数量:', fileInput.files.length);
                    console.log('文件名:', fileInput.files[0] ? fileInput.files[0].name : '无');
                    return true;
                }} else {{
                    console.log('未找到文件输入框');
                    return false;
                }}
                """
                
                result = self.tab.run_js(js_code)
                
                if result:
                    logging.info("✅ 方法3成功：JavaScript设置文件")
                    time.sleep(2)
                    self.check_upload_button_status()
                    return True
                else:
                    logging.warning("⚠️ 方法3失败：JavaScript返回false")
                    
            except Exception as e:
                logging.warning(f"⚠️ 方法3失败: {e}")
            
            logging.error("❌ 所有上传方法都失败")
            return False
            
        except Exception as e:
            logging.error(f"❌ 测试上传方法失败: {e}")
            return False
    
    def check_upload_button_status(self):
        """检查上传按钮状态"""
        try:
            upload_button = self.tab.ele('button:contains("Upload Invoice")', timeout=3)
            if upload_button:
                is_disabled = upload_button.attr('disabled')
                btn_text = upload_button.text
                logging.info(f"📋 上传按钮状态: text='{btn_text}', disabled={is_disabled}")
                
                if not is_disabled:
                    logging.info("✅ 上传按钮已启用，可以点击")
                    # 可以选择是否点击
                    # upload_button.click()
                    # logging.info("✅ 已点击上传按钮")
                else:
                    logging.warning("⚠️ 上传按钮仍然禁用")
            else:
                logging.warning("⚠️ 未找到上传按钮")
                
        except Exception as e:
            logging.warning(f"⚠️ 检查按钮状态失败: {e}")
    
    def run_test(self):
        """运行完整测试"""
        try:
            logging.info("🚀 开始文件上传逻辑测试...")

            # 1. 初始化浏览器
            if not self.init_browser():
                return False

            logging.info("⚠️ 请手动登录并导航到 https://app.augmentcode.com/promotions/cursor 页面")
            logging.info("⚠️ 登录完成后，请按Enter键继续测试...")
            input("按Enter键继续...")

            # 获取当前页面URL确认
            current_url = self.tab.url
            logging.info(f"🔍 当前URL: {current_url}")

            # 3. 分析页面元素
            if not self.analyze_upload_elements():
                return False

            # 4. 测试文件上传方法
            if not self.test_file_upload_methods():
                return False

            logging.info("✅ 测试完成")

            # 保持浏览器打开以便观察
            input("按Enter键关闭浏览器...")

            return True

        except Exception as e:
            logging.error(f"❌ 测试失败: {e}")
            return False
        finally:
            if self.tab:
                try:
                    self.tab.close()
                except:
                    pass

if __name__ == "__main__":
    tester = UploadTester()
    success = tester.run_test()
    
    if success:
        print("✅ 测试成功完成")
    else:
        print("❌ 测试失败")
