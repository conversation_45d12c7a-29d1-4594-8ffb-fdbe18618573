#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentCode 验证参数复用测试脚本
提取并复用 g-recaptcha-response 和 verisoul-session-id 参数
"""

import requests
import json
from urllib.parse import urlencode

# 代理配置
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:1080',
    'https': 'http://127.0.0.1:1080'
}

# 从第一个请求提取的验证参数
EXTRACTED_PARAMS = {
    # 第一个请求的 g-recaptcha-response
    'g_recaptcha_response_1': '03AFcWeA55fipSXnF41HZAgYSUrluTmcj955JFQl8ePtfz3haAcBt0NarXE7ai9NFokc4-z-4PNA8MH6uRIse_Cuk68W8F0S0QOpxcTz_Cas6HK8JWVQKQQz2vXj4NOjCPzYzIFg7XN8QnpDshdteRaMxj4qAR6f1A2zBBqV0cn7Vm_ArgekxzHVZVtigYXmvJFII1tD3paGdwCFbtf0FOBM3kSFYbpw4SsAUYedq2j8dpM-BDtiNztomTISIIi-E9J6lew755xrF9wElGHGKWGJBTd8dKE46hpQF3Ta7gnfun4S8KPYdZXLMcpQ9QHggXYtZEWY4pSQOPZoO_hVYJUYhLBGRYuREMSltpXRr2Ea7zsoR6Hm89l1K5mt72bZ62sehZs5sG2YlLktGm14ssfHrU4d_ZjPZrbZ7lLlzrmBEYjB1lns3SVDn9mzA77ZYnka-sge-UTnAayHISKE3ZjHfBP_-QHrhHEY6Yvs29y_W6pE0gFkJFoicKh0wDKsj9OcGLQf3HuIsKwjDtL4t2fJ0hZO3_PxHPH6PrWH0O19YtJps8LM2eedEirE6e4ZZkgL0tbgkCT2IvgZdXfNWc4nrISoODEMnnN7ls8zdGZZb17AkqRKiaMjszY6wQnBgDj-z070OB-u6yEbLksNUwyb8qKwTFtbCd1uBsHoOm-YcVAc8XN6x12PJzfIKzOiu4hk4zdcCFsYGXP_XOkdNaKq_IeCt1EBL8-gFh_lxdG2vp-gSt2cww_q248xR1qHFhULv3rbDI2kevZUY5dxubdVYn3rdYYisbWtrVtCwPKkDhvf2-0WjJntcT4ij_Pj9d88gBIbHExtNYHnJMR233BYsx-ZseeULWTq7eutSf9QlroUCcKR0Mhyjr5Ja7fDopkiYkhZwv-TPu0bKNnzIi3jRYFRyvSj-a8iJwKLCTrW9EzoVGLvzKv3twNvtfQD73wbGjH59lUysXSdjCvl83xLmwEFnaskDx1wFVwmBRk9tGWd5XdGk58qy3XWwBa7z2TLxxP6zlc9hQF5ZGoqHh337D6yiCIc5Xo7c-qW6lvjoiGvKzpCw66KZDm7mIl1KmWo6DMGdV8B2lguzz9p-cAoc3ZZLLASu3x4_8_j-N9jrGTL5H0osfNYJfq7GHYePFfmRciPQ5-Sa4I8jV6DZqZvI0-Dy23jakyxh5S3gxwMX-1jzQZ4Br7lSsdKjee18R1mRZXezuaTXnl-or3z8dasrS91eNk7Tjwg5vXwLEm-7vJY1ZupqV0WJltm_cdgUDuql3395ME97IFo3rfwg8NSDs0rD2hPHCyxcl6GphzuTAOb8GLjDZHkP7L24h11HRaBv_v-j7SzO4FfVZGSCqzbzzjnK4HE8N0hiJTZxSayRA25g0-rFQcHJOrsGf28z_vQt34gTClQrj5_sJN4FM7P13uOHaXhsf28ePLkrCQD1GrrCRGdSRsneYVXSAxcc5-ivjs9p07AyGRVYKhkRlAh-JcMh7uMRufvPjmwLDlFlcGaZGJSbxmF0t0RV8c3aZizFZWTojzZiZOt11Y5lmk6-yfW3jqBANVNvUu-ps9kNicm4IfbMRQ8_20ygPzgDtwOyTJvUol3QH6vR55YJ2HtNd9FRB3Bi4_Q4buSZjp_NsT47X4tCODFX-9kyJ1lIeUT5G_2e0tDX-WhRD3esDPh3nqNNlmu0Z_Su5tRucrTuxzQJEWqd42TGDW7FHsz5qkROodaLYBzGzbUgngwZaBWjTsdy-SQ7ID2cN_9YFCo3Iim38GoXs0DTWYGA3yc1PQjKzkM-D40XfD4PO3UaQejVLqw2Q_kv9bwbCcejzIQgROpCEJHcU6aC21UoGy1iYXaLTSyFlec11ThqkmduBqhOk-ZVppgSGBughoYogFW-04g0ApDauY5PiorXlPxA6jILsLh7YkW9ejXUvAMXN_q3aw7hpDys5jNjEo6GWaNO-B-V4mJwf12_QI7CRpyzwcr_EK9SHDlqR8QZtNVa0Y7QROe4hCzmSBIUkHHj1-Pwsw3Bke77aE6SfALhi-R3oJ75slAo94AiC-TBO9Zk63PkVO20HPSnGksWXHbdD8Li10_bltIK_xn_Mqqbh2_IbRyTwKEu21vOMzxRGEUbb4mQRZm-4shlmFp7fvEDZSkpppogutI32fpsJQjZk0_DFNYukHQdLDTHudPq8YOzItooZrmlJTtX4MYPqrIPyVg_aOBbi9cZ-D_iXyv0kcu9DQBRAq9-TWlKYfe9K40cY2QI9Vazi8rsMXGayb29YvvPjob3C-NESyLoy-AmX2iXpimbiKJNWOMOF9SRejwQJIKiktNHyZj3WUCgr97wthXig9cdtSFOxql0WRdT93dQMqeK6qouS9wuw',
    
    # verisoul-session-id
    'verisoul_session_id': '0d575ffb-e246-4687-aba0-c64f2a79b922',
    
    # 第二个请求的 recaptchaToken
    'recaptcha_token_2': '03AFcWeA4vQi39JHnnZ-y57MWrG9Dd09_ptdvlKvf3VnXR-Kg0lyKlKo0OYeykY9Wru5ymqhnpas3R-8g05YzAbxg-AvVFskW9qTGjrmfmfPV5H4zW0wDeGLMjBqQFViaYeDmauLBkX5CLpm0xEkTivxOBpFEaAj10vJlo7TMhqjIuHuHkE5DDJR2gCJEcHUYcfnvfQ5VlLG6VexhEywOkKpCL0E-z7xPKC2dagXppPgy0RTOvqOAdw6TGw_uzu-h0EX-Pl-MMWVEyQyVvz56zM3foP9eRvCLZgbZ5NarXckNAr1fJ9HLxaHTnI7DlH0mRvm1RqOrgYz1We3SNO9Dx9lyvBCaQ-frxLcM5E3_VCcof0Fun583pTRxktr8t3SUP0uOtiC5Fe5o3NzYbaatD7GbSb9jzcvWcBc8kBgDPVv5cFPwKciXlMTndlsMI4ELTmX1LF5GiqcXxqntX9Ez8133Y8kJrvP28HE3wA82Fqn9PtoW0SzC5_ZSY6YjWe1oZJzMfs8wwx7IbBcPkecw4gYh5g3JI2EALwkkpuRV_xcAw1rnoX8x76oMi00lGwPfTe-lQIbSHHWMhzTT5Bu2AR9skDiSxPM5PkJwzZyVL06FG9ovTUl7az4lqU6gpfgSQDKWOTdd6zPwX0snJj3yHV3lsSno1iSonQPYDOh0a4j_xsl456378l36PMM7t4Vfkx8kBJFL9bANocLgg67m8U_RLR2kazeyGbYhUufCVgn02e59PRZ633AQ1HMMMrkTFq4IW7Is9JWjnTVAA5HfVCoHYr8PFejqBxRVhBtV6eS8w5Vv9GoSqSx7yEu8eiu7JGMQPkujmv16GvbQzaJ-LyGqVj8h4_9aEAQSfLokszChobEKKsQQxOb_6fwFyBboue9M3M20g9hPnGkpqqf_ogvFmYcRiKAEJ6wzZOnjJvnFWxMtxLaAgGeuuEgDeHvNIjBqlt1X87PDaxK2jYuLkpYmlT0KIN5eKYM3rPLUSihBCaU5xR_VcY6zczrNn0nuG1kxI9CpG4LTfvW2piKJQcnGc2sW4-_bi4LR-VV4hn9KLDh53T9ULCqsx7LPv6c5tJN2t8WKRugpYnLAtZxMmeB6smJRP1FGD0O1EGsR4UbrpGeOOYXivIe2ci52NYFOnpuw-g3aSI8z6UvsYDh3e87UUihDqoCrp3b2H42A7dn4Ea9JDY0V9Jt5HbNGNgd2WUFwq_LoIK07N4bT44riusmDho6Djsk4Nn_6Zejg3S9MPzBInyYgyoZ6FmJuD6qGJjWiTgWfoqxIpnyhBTmoa3ziE6iGciaUcrFSa1B-cK3R55ukWfsYCeuuQnTR3yCRLO579ytTLa1fnphkAcVAd2aUhEgEl1Oz57mlaK7HEpohutJIXn720DHbGbD-fE5iAsI5M0sJgyQVVPLBDdqgy7aG6OqSzoXjsZti5d4nO30NghcZ1ECL1DQU8WpiVhf8eVL6J6mgK1TBZsbJT1YnnTi6yyM9rtf6VQEqOPFAWPHPi9d936AQpcrYTc8CnQ-qPlM6mEpvWy87DQFk0Idv4ak9QotH_X4vO1mucsY5IuAwoowov9qJsjLhatUQY9INkW6MGgquiQOJvXVmhCMR9kkX2Sf1dXIiXm4cIHYaNDGkAN9wwfItanUeLuuaYcp7-3Kifb-GwzJPovh6-V350m6Wbj8guNwgWJ-OzRZ0TlNCoNULhQGJFDzjH3mEqT7_6Unkna6IObHrrd7DEaKKIh3eokeu007NYKGKzEmXcDtfbEeDSJ9Rgj_CsoAaSkAmOS2yYIHE1Uz_dEf3iSweBpWvP09JnX0lLW94Aqtc-fj2ZJyowl6bcukqwmCSr3mnv2zBQEFp2fJq3axoY0GU2oLxfpptKOLr7c0dG4HIbZJtOOLkrbpX0_XmKBalME64t85t92FOWBCMpEIu-cTO7NVPpfWD3Ve2ze60PBoU9wldKesQ-lYhFf9Dm5YeQBKp7s3eDGTJXJZx5AyJCQ9xTn52npYPJTRgGVsZYVaO3ZPoXiho4Xnhh3Lby7tYODILz-2sh-BVTxY9DnDU-1oCHQZxK19efJpEbC2q29vKM6gfeATOrM2TixpJrQNa4hSKp-9G6LJsJNNbo3oM7fbbDPCcT6EPAw-TJfROlefHMNo7VRcDnUXE5Yr7ygJPdOXknUvxOUhNuqQM_LRVZ_8ERZjjGTpQ0rNYmw807bg1lS_zMxxlXSxLXQyT6SA13c8_u7waYfPjI9MLDb61hgtB8hpuKwnhpTj55aQoKazho2TZcb_wDHItT4I_xVXGwRy4HPOdHjFuYO4nwppV97-UsG4ep9bOw1DqRWhSRYwv_-MclCjGuRB40Av5Q-WsfMeBiUM0YPQrVFQIRHFADjpcGt7v3v3tb463vnA'
}

# 通用请求头
COMMON_HEADERS = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'en,zh;q=0.9,zh-CN;q=0.8',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

def test_terms_accept_request():
    """
    测试第一个请求：条款接受
    复用 g-recaptcha-response 和 verisoul-session-id
    """
    print("=== 测试条款接受请求 ===")
    
    url = 'https://auth.augmentcode.com/terms-accept'
    params = {
        'response_type': 'code',
        'client_id': 'customer-ui',
        'redirect_uri': 'https://app.augmentcode.com/auth/callback',
        'state': 'jHkjctC2gr7PjNTUvcf3gcPTFbG4lRhYBDYH8rZvlYo',
        'code_challenge': 'yD8hR6ukkVeDq00rHYlSLy_umJfiFUKCGTZms9Z8liY',
        'code_challenge_method': 'S256'
    }
    
    headers = COMMON_HEADERS.copy()
    headers.update({
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://auth.augmentcode.com',
        'priority': 'u=0, i',
        'referer': f'{url}?{urlencode(params)}',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1'
    })
    
    # 构建POST数据
    data = {
        'sign_up': 'true',
        'g-recaptcha-response': EXTRACTED_PARAMS['g_recaptcha_response_1'],
        'verisoul-session-id': EXTRACTED_PARAMS['verisoul_session_id'],
        'verosint-deviceid': '',
        'client-errors': '[]',
        'continue': 'continue',
        'terms-of-service': 'accepted'
    }
    
    try:
        response = requests.post(
            f"{url}?{urlencode(params)}",
            headers=headers,
            data=data,
            proxies=PROXY_CONFIG,
            timeout=30,
            allow_redirects=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容长度: {len(response.text)}")
        print(f"响应内容预览: {response.text[:500]}...")
        
        return response
        
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def test_promotions_request():
    """
    测试第二个请求：推广API
    复用 recaptchaToken
    """
    print("\n=== 测试推广API请求 ===")
    
    url = 'https://app.augmentcode.com/api/promotions'
    
    headers = {
        'accept': '*/*',
        'accept-language': 'en,zh;q=0.9,zh-CN;q=0.8',
        'content-type': 'multipart/form-data; boundary=----WebKitFormBoundarykFxQFEB9DfCN4aOg',
        'origin': 'https://app.augmentcode.com',
        'priority': 'u=1, i',
        'referer': 'https://app.augmentcode.com/promotions/cursor',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # 构建multipart数据
    files = {
        'id': (None, 'cursor_2025'),
        'file': ('WIPDF.pdf', b'', 'application/pdf'),
        'recaptchaToken': (None, EXTRACTED_PARAMS['recaptcha_token_2'])
    }
    
    try:
        response = requests.post(
            url,
            files=files,
            proxies=PROXY_CONFIG,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        return response
        
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def main():
    """主测试函数"""
    print("AugmentCode 验证参数复用测试")
    print("=" * 50)
    
    print("提取的参数:")
    print(f"g-recaptcha-response (第一个): {EXTRACTED_PARAMS['g_recaptcha_response_1'][:50]}...")
    print(f"verisoul-session-id: {EXTRACTED_PARAMS['verisoul_session_id']}")
    print(f"recaptchaToken (第二个): {EXTRACTED_PARAMS['recaptcha_token_2'][:50]}...")
    print()
    
    # 测试第一个请求
    response1 = test_terms_accept_request()
    
    # 测试第二个请求
    response2 = test_promotions_request()
    
    print("\n=== 测试总结 ===")
    if response1:
        print(f"条款接受请求: 状态码 {response1.status_code}")
    if response2:
        print(f"推广API请求: 状态码 {response2.status_code}")

if __name__ == "__main__":
    main()
